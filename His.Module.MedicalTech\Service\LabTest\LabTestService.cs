﻿using Furion.DatabaseAccessor;
using His.Module.MedicalTech.Api.LabTest;
using His.Module.MedicalTech.Api.LabTest.Dto;
using His.Module.MedicalTech.Enum;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.Shared.Api.Api.ChargeItem;
using Yitter.IdGenerator;
namespace His.Module.MedicalTech.Service;

/// <summary>
/// 检验表服务 🧩
/// </summary>
[ApiDescriptionSettings(MedicalTechConst.GroupName, Order = 100)]
public class LabTestService(
    SqlSugarRepository<LabTest> labTestRep,
    SqlSugarRepository<LabTestPackageItem> labTestPackageItemRep,
    IChargeItemApi chargeItemApi,
    IChargeApi chargeApi)
    : IDynamicApiController, ITransient, ILabTestApi
{

    /// <summary>
    /// 分页查询检验表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询检验表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<LabTestOutput>> Page(PageLabTestInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = labTestRep.AsQueryable()
            .Where(u => u.RegisterId == input.RegisterId)
            .Where(u => u.BillingDoctorId == long.Parse(App.User.FindFirst(ClaimConst.UserId).Value))
            .Where(u => u.BillingDeptId == long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value))
            .Where(u => u.BillingTime >= DateTime.Today)
            .Where(u => u.Flag == input.Flag)
            .OrderBy(u => u.ApplyNo)
            .Select<LabTestOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取检验表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取检验表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<LabTest> Detail([FromQuery] QueryByIdLabTestInput input)
    {
        return await labTestRep.AsQueryable()
            .Includes(u => u.LabTestPackageItem)
            .FirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加检验表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加检验表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost, UnitOfWork]
    public async Task Add(List<AddLabTestInput> input)
    {
        // 将输入对象映射为 LabTest 实体列表
        var entities = input.Adapt<List<LabTest>>();
        // 批量获取所有关联的收费项目详细信息（优化数据库查询，避免N+1问题）
        var chargeItems = await chargeItemApi.GetDetails([.. entities.Select(u => (long)u.ItemId)]);
        // 遍历处理每个检验实体
        foreach (var entity in entities)
        {
            // 根据 ItemId 匹配对应的收费项目
            var chargeItem = chargeItems.FirstOrDefault(u => u.Id == entity.ItemId);
            // 生成分布式唯一ID
            entity.Id = YitIdHelper.NextId();
            // 从数据库序列生成申请单号（格式化为8位数字，不足补零）
            entity.ApplyNo = await labTestRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('apply_no_seq') As varchar), 8, '0')");
            // 填充收费项目基础信息
            entity.ItemCode = chargeItem.Code;
            entity.ItemName = chargeItem.Name;
            entity.Unit = chargeItem.Unit;
            entity.Price = chargeItem.Price;
            // 计算总金额 = 数量 × 单价
            entity.Amount = entity.Quantity * chargeItem.Price;
            // 转换套餐标识为可空整型
            entity.IsPackage = (int?)chargeItem.Package;
            // 设置业务时间相关字段
            entity.BillingTime = DateTime.Now;
            // 从用户凭证中获取并设置计费部门信息
            entity.BillingDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId)?.Value ?? "0");
            entity.BillingDeptName = App.User.FindFirst(ClaimConst.OrgName)?.Value;
            // 从用户凭证中获取并设置开单医生信息
            entity.BillingDoctorId = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");
            entity.BillingDoctorName = App.User.FindFirst(ClaimConst.RealName)?.Value;
            // 设置收费分类和初始状态
            entity.ChargeCategoryId = chargeItem.ChargeCategoryId;
            entity.Status = 1;
            // 处理套餐项目（当收费项目是套餐时）
            if (chargeItem.Package == YesNoEnum.Y)
            {
                // 转换套餐子项为处置包项实体
                var labTestPackageItems = chargeItem.ChargeItemPacks.Select(packageItem => new LabTestPackageItem
                {
                    // 关联主处置记录
                    LabTestId = entity.Id,
                    ApplyNo = entity.ApplyNo,
                    // 填充套餐子项信息
                    ItemId = packageItem.Id,
                    ItemCode = packageItem.Code,
                    ItemName = packageItem.Name,
                    Unit = packageItem.Unit,
                    Price = packageItem.Price,
                    Quantity = entity.Quantity * packageItem.Quantity,
                    // 计算子项金额
                    Amount = entity.Quantity * packageItem.Quantity * packageItem.Price,
                    ChargeCategoryId = packageItem.ChargeCategoryId,
                }).ToList();
                // 批量插入套餐子项记录
                await labTestPackageItemRep.InsertRangeAsync(labTestPackageItems);
            }
        }
        // 批量插入主处置记录（优化数据库操作）
        await labTestRep.InsertRangeAsync(entities);
    }

    /// <summary>
    /// 更新检验表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新检验表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost, UnitOfWork]
    public async Task Update(UpdateLabTestInput input)
    {
        var entity = input.Adapt<LabTest>();
        await labTestRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除检验表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除检验表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost, UnitOfWork]
    public async Task Delete(DeleteLabTestInput input)
    {
        var entity = await labTestRep.GetFirstAsync(u => u.Id == input.Id)
                     ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0002);
        }
        await labTestRep.FakeDeleteAsync(entity); //假删除
        var packEntity = await labTestPackageItemRep.AsQueryable().Where(u => u.LabTestId == entity.Id).ToListAsync();
        await labTestPackageItemRep.FakeDeleteAsync(packEntity); //假删除
        //await _labTestRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除检验表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除检验表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteLabTestInput> input)
    {
        var exp = Expressionable.Create<LabTest>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await labTestRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await labTestRep.FakeDeleteAsync(list); //假删除
        //return await _labTestRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置申请单状态 🔄
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task SetStatus(SetLabTestStatusInput input)
    {
        var entity = await labTestRep.GetFirstAsync(u => u.Id == input.Id)
                     ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (!MedicalTechConst.ApplyStatusValidTransitions.TryGetValue((int)entity.Status, out var allowed)
            || !allowed.Contains((int)input.Status))
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0004);
        }
        await labTestRep.UpdateAsync(u => new LabTest
        {
            Status = input.Status,
        }, u => u.Id == entity.Id);
    }

    /// <summary>
    /// 检验收费 💰
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("检验收费")]
    [ApiDescriptionSettings(Name = "Charge"), HttpPost, UnitOfWork]
    public async Task Charge(ChargeLabTestInput input)
    {
        // 查询主记录及其明细
        var main = await labTestRep.AsQueryable()
                       .Includes(u => u.LabTestPackageItem)
                       .FirstAsync(u => u.Id == input.Id)
                   ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (main.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0006);
        }

        // 构建 DTO 对象
        var dto = main.Adapt<OutpatientChargeDto>();
        dto.BillingType = "LabTest";
        dto.BillingId = main.Id;
        dto.BillingNo = main.ApplyNo;
        dto.TotalAmount = main.Amount;
        dto.InvoiceNumber = input.InvoiceNumber;
        // 处理套餐项目
        if (main.IsPackage == 1)
        {
            dto.Details = main.LabTestPackageItem.Select(item => new OutpatientChargeDetailDto
            {
                ItemId = item.ItemId,
                ItemCode = item.ItemCode,
                ItemName = item.ItemName,
                Unit = item.Unit,
                Price = item.Price,
                Quantity = item.Quantity,
                Amount = item.Amount,
                BillingId = item.LabTestId,
                BillingNo = item.ApplyNo,
                BillingTime = main.BillingTime,
                BillingDeptId = main.BillingDeptId,
                BillingDoctorId = main.BillingDoctorId,
                ExecuteDeptId = main.ExecuteDeptId,
                BillingDetailId = item.Id,
                BillingDoctorName = main.BillingDoctorName,
                ChargeCategoryId = item.ChargeCategoryId,
                PackId = main.ItemId,
                PackCode = main.ItemCode,
                PackName = main.ItemName,
                PackNumber = (short?)(item.Quantity / main.Quantity),
                PackPrice = main.Price,
            }).ToList();
        }
        // 处理非套餐项目
        else if (main.IsPackage == 2)
        {
            var detail = new OutpatientChargeDetailDto()
            {
                ItemId = main.ItemId,
                ItemCode = main.ItemCode,
                ItemName = main.ItemName,
                Unit = main.Unit,
                Price = main.Price,
                Quantity = main.Quantity,
                Amount = main.Amount,
                BillingId = main.Id,
                BillingNo = main.ApplyNo,
                BillingTime = main.BillingTime,
                BillingDeptId = main.BillingDeptId,
                BillingDoctorId = main.BillingDoctorId,
                ExecuteDeptId = main.ExecuteDeptId,
                BillingDetailId = 0, // 非套餐收费时明细ID为0
                BillingDoctorName = main.BillingDoctorName,
                ChargeCategoryId = main.ChargeCategoryId,
            };
            dto.Details = [detail];
        }

        // 调用收费接口
        var result = await chargeApi.Add(dto);

        // 更新主记录状态和收费信息
        await labTestRep.UpdateAsync(u => new LabTest
        {
            Status = 2,
            ChargeTime = result.ChargeTime,
            ChargeStaffId = result.ChargeStaffId,
            ChargeStaffName = result.ChargeStaffName,
        }, u => u.Id == input.Id);
    }
    /// <summary>
    /// 检验收费 💰
    /// </summary>
    /// <param name="input"></param>
    [DisplayName("检验收费")]
    [ApiDescriptionSettings(IgnoreApi = true)]
    public async Task Charge(ChargeLabTestDto input)
    {
        await Charge(new ChargeLabTestInput
        {
            Id = input.Id, InvoiceNumber = input.InvoiceNumber
        });
    }
}