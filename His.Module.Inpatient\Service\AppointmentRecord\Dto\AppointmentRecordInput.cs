﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Inpatient;

/// <summary>
/// 住院预约基础输入参数
/// </summary>
public class AppointmentRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 预约时间
    /// </summary>
    public virtual DateTime? AppointmentTime { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public virtual string? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    public virtual string? IdCardNo { get; set; }
    /// <summary>
    /// 就诊卡id
    /// </summary>
    public virtual long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string? MedicalCardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    public virtual string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室代码
    /// </summary>
    public virtual string? DeptCode { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生代码
    /// </summary>
    public virtual string? DoctorCode { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 诊断代码
    /// </summary>
    public virtual string? DiagnosticCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    public virtual string? DiagnosticName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 住院预约分页查询输入参数
/// </summary>
public class PageAppointmentRecordInput : BasePageInput
{
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    /// <summary>
    /// 就诊卡id
    /// </summary>
    public virtual long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string? MedicalCardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 预约时间范围
    /// </summary>
     public DateTime?[] AppointmentTimeRange { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public string? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室代码
    /// </summary>
    public string? DeptCode { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生代码
    /// </summary>
    public string? DoctorCode { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 诊断代码
    /// </summary>
    public string? DiagnosticCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosticName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 住院预约增加输入参数
/// </summary>
public class AddAppointmentRecordInput
{
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "患者姓名字符长度不能超过100")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 预约时间
    /// </summary>
    public DateTime? AppointmentTime { get; set; }
    /// <summary>
    /// 就诊卡id
    /// </summary>
    public virtual long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string? MedicalCardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [MaxLength(100, ErrorMessage = "证件类型字符长度不能超过100")]
    public string? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    [MaxLength(100, ErrorMessage = "证件号码字符长度不能超过100")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    [MaxLength(100, ErrorMessage = "保险号字符长度不能超过100")]
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室代码
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约科室代码字符长度不能超过100")]
    public string? DeptCode { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约科室名称字符长度不能超过100")]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生代码
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约医生代码字符长度不能超过100")]
    public string? DoctorCode { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约医生姓名字符长度不能超过100")]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 诊断代码
    /// </summary>
    [MaxLength(100, ErrorMessage = "诊断代码字符长度不能超过100")]
    public string? DiagnosticCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "诊断名称字符长度不能超过100")]
    public string? DiagnosticName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注字符长度不能超过500")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
}

/// <summary>
/// 住院预约删除输入参数
/// </summary>
public class DeleteAppointmentRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}
 
/// <summary>
/// 住院预约主键查询输入参数
/// </summary>
public class QueryByIdAppointmentRecordInput : DeleteAppointmentRecordInput
{
}

/// <summary>
/// 住院预约数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportAppointmentRecordInput : BaseImportInput
{
    /// <summary>
    /// 患者ID
    /// </summary>
    [ImporterHeader(Name = "患者ID")]
    [ExporterHeader("患者ID", Format = "", Width = 25, IsBold = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [ImporterHeader(Name = "患者姓名")]
    [ExporterHeader("患者姓名", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 预约时间
    /// </summary>
    [ImporterHeader(Name = "预约时间")]
    [ExporterHeader("预约时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? AppointmentTime { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [ImporterHeader(Name = "证件类型")]
    [ExporterHeader("证件类型", Format = "", Width = 25, IsBold = true)]
    public string? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    [ImporterHeader(Name = "证件号码")]
    [ExporterHeader("证件号码", Format = "", Width = 25, IsBold = true)]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    [ImporterHeader(Name = "保险号")]
    [ExporterHeader("保险号", Format = "", Width = 25, IsBold = true)]
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    [ImporterHeader(Name = "预约科室ID")]
    [ExporterHeader("预约科室ID", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室代码
    /// </summary>
    [ImporterHeader(Name = "预约科室代码")]
    [ExporterHeader("预约科室代码", Format = "", Width = 25, IsBold = true)]
    public string? DeptCode { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    [ImporterHeader(Name = "预约科室名称")]
    [ExporterHeader("预约科室名称", Format = "", Width = 25, IsBold = true)]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    [ImporterHeader(Name = "预约医生ID")]
    [ExporterHeader("预约医生ID", Format = "", Width = 25, IsBold = true)]
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生代码
    /// </summary>
    [ImporterHeader(Name = "预约医生代码")]
    [ExporterHeader("预约医生代码", Format = "", Width = 25, IsBold = true)]
    public string? DoctorCode { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [ImporterHeader(Name = "预约医生姓名")]
    [ExporterHeader("预约医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 诊断代码
    /// </summary>
    [ImporterHeader(Name = "诊断代码")]
    [ExporterHeader("诊断代码", Format = "", Width = 25, IsBold = true)]
    public string? DiagnosticCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    [ImporterHeader(Name = "诊断名称")]
    [ExporterHeader("诊断名称", Format = "", Width = 25, IsBold = true)]
    public string? DiagnosticName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
}
