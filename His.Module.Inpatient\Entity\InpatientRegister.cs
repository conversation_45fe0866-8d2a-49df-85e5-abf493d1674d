﻿using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 住院登记表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("inpatient_register", "住院登记表")]
public class InpatientRegister : EntityTenant
{
    /// <summary>
    /// 预约记录id
    /// </summary>
    [SugarColumn(ColumnName = "appointment_record_id", ColumnDescription = "预约记录id")]
    public virtual long? AppointmentRecordId { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者编号
    /// </summary>
    [SugarColumn(ColumnName = "patient_no", ColumnDescription = "患者编号", Length = 100)]
    public virtual string? PatientNo { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 100)]
    public virtual string? PatientName { get; set; }
    
    // /// <summary>
    // /// 证件类型
    // /// </summary>
    // [SugarColumn(ColumnName = "card_type", ColumnDescription = "证件类型", Length = 100)]
    // public virtual string? CardType { get; set; }
    
    /// <summary>
    /// 证件类型（编码）
    /// </summary>
    [SugarColumn(ColumnName = "id_card_type", ColumnDescription = "证件类型（编码）")]
    public virtual int? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    [SugarColumn(ColumnName = "id_card_no", ColumnDescription = "证件号码", Length = 100)]
    public virtual string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    [SugarColumn(ColumnName = "insurance_no", ColumnDescription = "保险号", Length = 100)]
    public virtual string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 就诊卡ID
    /// </summary>
    [SugarColumn(ColumnName = "medical_card_id", ColumnDescription = "就诊卡ID")]
    public virtual long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnName = "medical_card_no", ColumnDescription = "就诊卡号", Length = 100)]
    public virtual string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 100)]
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_no", ColumnDescription = "住院号", Length = 100)]
    public virtual string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_serial_no", ColumnDescription = "住院流水号", Length = 100)]
    public virtual string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院次数
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_times", ColumnDescription = "住院次数")]
    public virtual int? InpatientTimes { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    [SugarColumn(ColumnName = "medical_record_no", ColumnDescription = "病案号", Length = 100)]
    public virtual string? MedicalRecordNo { get; set; }
    /// <summary>
    /// 费别Id
    /// </summary>
    [SugarColumn(ColumnName = "fee_id", ColumnDescription = "费别Id")]
    public virtual  long? FeeId { get; set; }
    /// <summary>
    /// 费别
    /// </summary>
    [SugarColumn(ColumnName = "fee_name", ColumnDescription = "费别")]
    public virtual  string? FeeName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "医生ID")]
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "doctor_name", ColumnDescription = "医生姓名", Length = 100)]
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室ID")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "科室名称", Length = 100)]
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 主治医生ID
    /// </summary>
    [SugarColumn(ColumnName = "main_doctor_id", ColumnDescription = "主治医生ID")]
    public virtual long? MainDoctorId { get; set; }
    
    /// <summary>
    /// 主治医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "main_doctor_name", ColumnDescription = "主治医生姓名", Length = 100)]
    public virtual string? MainDoctorName { get; set; }
    
    /// <summary>
    /// 入院诊断代码
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_diagnostic_code", ColumnDescription = "入院诊断代码", Length = 100)]
    public virtual string? InpatientDiagnosticCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_diagnostic_name", ColumnDescription = "入院诊断名称", Length = 100)]
    public virtual string? InpatientDiagnosticName { get; set; }
    
    /// <summary>
    /// 次要诊断代码
    /// </summary>
    [SugarColumn(ColumnName = "secondary_diagnostic_code", ColumnDescription = "次要诊断代码", Length = 100)]
    public virtual string? SecondaryDiagnosticCode { get; set; }
    
    /// <summary>
    /// 次要诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "secondary_diagnostic_name", ColumnDescription = "次要诊断名称", Length = 100)]
    public virtual string? SecondaryDiagnosticName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_way", ColumnDescription = "入院途径", Length = 100)]
    public virtual string? InpatientWay { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_time", ColumnDescription = "入院时间")]
    public virtual DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 是否允许欠费
    /// </summary>
    [SugarColumn(ColumnName = "allow_arrears", ColumnDescription = "是否允许欠费")]
    public virtual bool? AllowArrears { get; set; }
    
    /// <summary>
    /// 欠费上限
    /// </summary>
    [SugarColumn(ColumnName = "arrears_limit", ColumnDescription = "欠费上限", Length = 20, DecimalDigits=4)]
    public virtual decimal? ArrearsLimit { get; set; }
    
    /// <summary>
    /// 担保人
    /// </summary>
    [SugarColumn(ColumnName = "guarantee_person", ColumnDescription = "担保人", Length = 100)]
    public virtual string? GuaranteePerson { get; set; }
    
    /// <summary>
    /// 新生儿出生体重g
    /// </summary>
    [SugarColumn(ColumnName = "newborn_birth_weight1", ColumnDescription = "新生儿出生体重g")]
    public virtual int? NewbornBirthWeight1 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    [SugarColumn(ColumnName = "newborn_birth_weight2", ColumnDescription = "新生儿出生体重单位g")]
    public virtual int? NewbornBirthWeight2 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    [SugarColumn(ColumnName = "newborn_birth_weight3", ColumnDescription = "新生儿出生体重单位g")]
    public virtual int? NewbornBirthWeight3 { get; set; }
    
    /// <summary>
    /// 新生儿入院体重g
    /// </summary>
    [SugarColumn(ColumnName = "newborn_inpatient_weight", ColumnDescription = "新生儿入院体重g")]
    public virtual int? NewbornInpatientWeight { get; set; }
    
    /// <summary>
    /// 是否有医保卡
    /// </summary>
    [SugarColumn(ColumnName = "has_medical_insurance", ColumnDescription = "是否有医保卡")]
    public virtual bool? HasMedicalInsurance { get; set; }
    /// <summary>
    /// 联系人
    /// </summary>
    [SugarColumn(ColumnName = "contact_name", ColumnDescription = "联系人")]
    public virtual string ContactName { get; set; }

    // 联系电话
    [SugarColumn(ColumnName = "contact_phone", ColumnDescription = "联系电话")]
    public virtual string ContactPhone { get; set; }
    
    /// <summary>
    /// 联系人关系
    /// </summary>
    [SugarColumn(ColumnName = "contact_relationship", ColumnDescription = "联系人关系")]
    public virtual string ContactRelationship { get; set; }
    [SugarColumn(ColumnName = "contact_address",IsJson = true , 
        ColumnDescription = "联系人地址")]
    public List<String>    ContactAddress { get; set; }
 

    // 联系电话
    [SugarColumn(ColumnName = "contact_address_street", ColumnDescription = "联系地区")]
    public   string ContactAddressStreet { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 创建组织ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建组织ID")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建组织名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建组织名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
