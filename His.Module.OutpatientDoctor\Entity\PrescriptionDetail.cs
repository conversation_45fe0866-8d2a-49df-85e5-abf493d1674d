﻿namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 处方明细表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("prescription_detail", "处方明细表")]
public class PrescriptionDetail : EntityTenantBaseData
{
    /// <summary>
    /// 处方主表Id
    /// </summary>
    [SugarColumn(ColumnName = "prescription_id", ColumnDescription = "处方主表Id")]
    public virtual long? PrescriptionId { get; set; }

    /// <summary>
    /// 药品Id
    /// </summary>
    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品Id")]
    public virtual long? DrugId { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }

    /// <summary>
    /// 药品类型
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类型", Length = 25)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 100)]
    public virtual string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "规格", Length = 100)]
    public virtual string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 100)]
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Quantity { get; set; }

    /// <summary>
    /// 单次量
    /// </summary>
    [SugarColumn(ColumnName = "single_dose", ColumnDescription = "单次量", Length = 16, DecimalDigits = 4)]
    public virtual decimal? SingleDose { get; set; }

    /// <summary>
    /// 单次量单位
    /// </summary>
    [SugarColumn(ColumnName = "single_dose_unit", ColumnDescription = "单次量单位", Length = 16)]
    public virtual string? SingleDoseUnit { get; set; }

    /// <summary>
    /// 给药途径Id
    /// </summary>
    [SugarColumn(ColumnName = "medication_routes_id", ColumnDescription = "给药途径Id")]
    public virtual long? MedicationRoutesId { get; set; }

    /// <summary>
    /// 给药途径名称
    /// </summary>
    [SugarColumn(ColumnName = "medication_routes_name", ColumnDescription = "给药途径名称", Length = 100)]
    public virtual string? MedicationRoutesName { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    [SugarColumn(ColumnName = "frequency_id", ColumnDescription = "频次Id")]
    public virtual long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    [SugarColumn(ColumnName = "frequency_name", ColumnDescription = "频次名称", Length = 100)]
    public virtual string? FrequencyName { get; set; }

    /// <summary>
    /// 用药天数
    /// </summary>
    [SugarColumn(ColumnName = "medication_days", ColumnDescription = "用药天数")]
    public virtual Int16? MedicationDays { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "单价", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Price { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Amount { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer", ColumnDescription = "生产厂家", Length = 256)]
    public virtual string? Manufacturer { get; set; }

    // /// <summary>
    // /// 药房Id
    // /// </summary>
    // [SugarColumn(ColumnName = "pharmacy_id", ColumnDescription = "药房Id")]
    // public virtual long? PharmacyId { get; set; }
    //
    // /// <summary>
    // /// 药房名称
    // /// </summary>
    // [SugarColumn(ColumnName = "pharmacy_name", ColumnDescription = "药房名称", Length = 100)]
    // public virtual string? PharmacyName { get; set; }
    /// <summary>
    /// 药房Id
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "药房Id")]
    public virtual long? StorageId { get; set; }

    /// <summary>
    /// 药房名称
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "药房名称", Length = 100)]
    public virtual string? StorageName { get; set; }
    [SugarColumn(ColumnName = "inventory_id", ColumnDescription = "库存id")]
    public virtual long? InventoryId { get; set; }
    /// <summary>
    /// 组标志
    /// </summary>
    [SugarColumn(ColumnName = "group_flag", ColumnDescription = "组标志", Length = 16)]
    public virtual string? GroupFlag { get; set; }

    /// <summary>
    /// 组号
    /// </summary>
    [SugarColumn(ColumnName = "group_no", ColumnDescription = "组号", Length = 16)]
    public virtual string? GroupNo { get; set; }

    /// <summary>
    /// 药品限制标志
    /// </summary>
    [SugarColumn(ColumnName = "drug_limit_flag", ColumnDescription = "药品限制标志")]
    public virtual Int16? DrugLimitFlag { get; set; }

    /// <summary>
    /// 药品待发标志
    /// </summary>
    [SugarColumn(ColumnName = "drug_pending_flag", ColumnDescription = "药品待发标志")]
    public virtual Int16? DrugPendingFlag { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_id", ColumnDescription = "收费类别Id")]
    public virtual long? ChargeCategoryId { get; set; }
    /// <summary>
    /// 收费类别编号
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_code", ColumnDescription = "收费类别编号")]
    public virtual string? ChargeCategoryCode { get; set; }
    
    /// <summary>
    /// 剂量单位
    /// </summary>
    [SugarColumn(ColumnName = "dosage_unit", ColumnDescription = "剂量单位", Length = 100)]
    public virtual string? DosageUnit { get; set; }

    /// <summary>
    /// 剂量值
    /// </summary>
    [SugarColumn(ColumnName = "dosage_value", ColumnDescription = "剂量值", Length = 16, DecimalDigits = 4)]
    public virtual decimal? DosageValue { get; set; }

    /// <summary>
    /// 含量
    /// </summary>
    [SugarColumn(ColumnName = "content_value", ColumnDescription = "含量", Length = 16, DecimalDigits = 4)]
    public virtual decimal? ContentValue { get; set; }

    /// <summary>
    /// 含量单位
    /// </summary>
    [SugarColumn(ColumnName = "content_unit", ColumnDescription = "含量单位", Length = 100)]
    public virtual string? ContentUnit { get; set; }

    /// <summary>
    /// 门诊包装数量
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_package_quantity", ColumnDescription = "门诊包装数量")]
    public virtual int? OutpatientPackageQuantity { get; set; }

    /// <summary>
    /// 最小包装单位
    /// </summary>
    [SugarColumn(ColumnName = "min_package_unit", ColumnDescription = "最小包装单位", Length = 100)]
    public virtual string? MinPackageUnit { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_staff_id", ColumnDescription = "收费人员Id")]
    public virtual long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    [SugarColumn(ColumnName = "charge_time", ColumnDescription = "收费时间")]
    public virtual DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 退费人员Id
    /// </summary>
    [SugarColumn(ColumnName = "refund_staff_id", ColumnDescription = "退费人员Id")]
    public virtual long? RefundStaffId { get; set; }

    /// <summary>
    /// 退费时间
    /// </summary>
    [SugarColumn(ColumnName = "refund_time", ColumnDescription = "退费时间")]
    public virtual DateTime? RefundTime { get; set; }

    /// <summary>
    /// 库存零售价
    /// </summary>
    [SugarColumn(ColumnName = "inventory_sale_price", ColumnDescription = "库存零售价", Length = 16, DecimalDigits = 4)]
    public virtual decimal? InventorySalePrice { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    [SugarColumn(ColumnName = "self_pay_ratio", ColumnDescription = "自付比例", Length = 4, DecimalDigits = 4)]
    public virtual decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    [SugarColumn(ColumnName = "is_ratio_audit", ColumnDescription = "自付比例是否审核 1审核 2不审核")]
    public virtual int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_time", ColumnDescription = "自付比例审核时间")]
    public virtual DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人Id
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_staff_id", ColumnDescription = "自付比例审核人Id")]
    public virtual long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人名称
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_staff_name", ColumnDescription = "自付比例审核人名称", Length = 64)]
    public virtual string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 用药方式 1治疗用药 2预防用药
    /// </summary>
    [SugarColumn(ColumnName = "medication_method", ColumnDescription = "用药方式 1治疗用药 2预防用药")]
    public virtual Int16? MedicationMethod { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "国家医保编码", Length = 100)]
    public virtual string? MedicineCode { get; set; }

    /// <summary>
    /// 用法Id
    /// </summary>
    [SugarColumn(ColumnName = "usage_id", ColumnDescription = "用法Id")]
    public virtual long? UsageId { get; set; }

    /// <summary>
    /// 用法编码
    /// </summary>
    [SugarColumn(ColumnName = "usage_code", ColumnDescription = "用法编码", Length = 64)]
    public virtual string? UsageCode { get; set; }

    /// <summary>
    /// 用法名称
    /// </summary>
    [SugarColumn(ColumnName = "usage_name", ColumnDescription = "用法名称", Length = 64)]
    public virtual string? UsageName { get; set; }

    /// <summary>
    /// 是否皮试
    /// </summary>
    [SugarColumn(ColumnName = "is_skin_test", ColumnDescription = "是否皮试")]
    public virtual int? IsSkinTest { get; set; }

    /// <summary>
    /// 皮试结果
    /// </summary>
    [SugarColumn(ColumnName = "skin_test_results", ColumnDescription = "皮试结果")]
    public virtual string? SkinTestResults { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
}