<template>
	<el-dialog
		v-model="state.isShowDialog"
		title="录入皮试结果"
		width="600px"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		draggable
	>
		<el-form
			:model="state.ruleForm"
			:rules="state.rules"
			ref="ruleFormRef"
			label-width="100px"
			v-loading="state.loading"
		>
			<!-- 药品信息 -->
			<el-card shadow="never" style="margin-bottom: 15px">
				<template #header>
					<span>药品信息</span>
				</template>
				<el-descriptions :column="2" border>
					<el-descriptions-item label="患者姓名">{{ state.drugInfo.patientName }}</el-descriptions-item>
					<el-descriptions-item label="就诊号">{{ state.drugInfo.visitNo }}</el-descriptions-item>
					<el-descriptions-item label="药品名称">{{ state.drugInfo.drugName }}</el-descriptions-item>
					<el-descriptions-item label="药品编码">{{ state.drugInfo.drugCode }}</el-descriptions-item>
					<el-descriptions-item label="规格">{{ state.drugInfo.spec }}</el-descriptions-item>
					<el-descriptions-item label="给药途径">{{ state.drugInfo.medicationRoutesName }}</el-descriptions-item>
				</el-descriptions>
			</el-card>

			<!-- 皮试结果录入 -->
			<el-form-item label="皮试结果" prop="skinTestResult">
				<el-radio-group v-model="state.ruleForm.skinTestResult">
					<el-radio :value="'1'">
						<el-tag type="success">阴性</el-tag>
						<span style="margin-left: 8px">皮试无反应，可以使用该药品</span>
					</el-radio>
					<el-radio :value="'2'">
						<el-tag type="danger">阳性</el-tag>
						<span style="margin-left: 8px">皮试有反应，禁止使用该药品</span>
					</el-radio>
					<el-radio :value="'3'">
						<el-tag type="warning">可疑</el-tag>
						<span style="margin-left: 8px">皮试反应不明确，需要重新皮试</span>
					</el-radio>
				</el-radio-group>
			</el-form-item>

			<el-form-item label="皮试时间" prop="skinTestTime">
				<el-date-picker
					v-model="state.ruleForm.skinTestTime"
					type="datetime"
					placeholder="选择皮试时间"
					format="YYYY-MM-DD HH:mm:ss"
					value-format="YYYY-MM-DD HH:mm:ss"
					style="width: 100%"
				/>
			</el-form-item>

			<el-form-item label="皮试方法">
				<el-input
					v-model="state.ruleForm.skinTestMethod"
					placeholder="请输入皮试方法，如：皮内注射"
					maxlength="100"
					show-word-limit
				/>
			</el-form-item>

			<el-form-item label="皮试剂量">
				<el-input
					v-model="state.ruleForm.skinTestDose"
					placeholder="请输入皮试剂量，如：0.1ml"
					maxlength="50"
					show-word-limit
				/>
			</el-form-item>

			<el-form-item label="反应描述">
				<el-input
					v-model="state.ruleForm.reactionDescription"
					type="textarea"
					:rows="3"
					placeholder="请描述皮试反应情况"
					maxlength="1000"
					show-word-limit
				/>
			</el-form-item>

			<el-form-item label="备注">
				<el-input
					v-model="state.ruleForm.remark"
					type="textarea"
					:rows="2"
					placeholder="请输入备注信息"
					maxlength="500"
					show-word-limit
				/>
			</el-form-item>
		</el-form>

		<template #footer>
			<el-button @click="closeDialog">取消</el-button>
			<el-button type="primary" @click="handleSubmit" :loading="state.loading">确定</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="addSkinTestDialog">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useSkinTestResultApi } from '/@/api/outpatientDoctor/skinTestResult';

// API
const skinTestResultApi = useSkinTestResultApi();

// 组件引用
const ruleFormRef = ref();

// 响应式数据
const state = reactive({
	isShowDialog: false,
	loading: false,
	drugInfo: {} as any,
	ruleForm: {
		prescriptionDetailId: 0,
		skinTestResult: '',
		skinTestTime: '',
		skinTestMethod: '',
		skinTestDose: '',
		reactionDescription: '',
		remark: '',
	},
	rules: {
		skinTestResult: [{ required: true, message: '请选择皮试结果', trigger: 'change' }],
		skinTestTime: [{ required: true, message: '请选择皮试时间', trigger: 'change' }],
	},
});

// 事件定义
const emit = defineEmits(['refresh']);

// 打开弹窗
const openDialog = (row: any) => {
	state.drugInfo = { ...row };
	state.ruleForm = {
		prescriptionDetailId: row.id,
		skinTestResult: '',
		skinTestTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
		skinTestMethod: '皮内注射',
		skinTestDose: '0.1ml',
		reactionDescription: '',
		remark: '',
	};
	state.isShowDialog = true;
};

// 提交
const handleSubmit = async () => {
	try {
		const valid = await ruleFormRef.value?.validate();
		if (!valid) return;

		state.loading = true;
		await skinTestResultApi.add(state.ruleForm);
		ElMessage.success('录入皮试结果成功');
		closeDialog();
	} catch (error) {
		console.error('录入皮试结果失败:', error);
		ElMessage.error('录入皮试结果失败');
	} finally {
		state.loading = false;
	}
};

// 关闭弹窗
const closeDialog = () => {
	state.isShowDialog = false;
	state.drugInfo = {};
	state.ruleForm = {
		prescriptionDetailId: 0,
		skinTestResult: '',
		skinTestTime: '',
		skinTestMethod: '',
		skinTestDose: '',
		reactionDescription: '',
		remark: '',
	};
	ruleFormRef.value?.resetFields();
	emit('refresh');
};

// 暴露方法
defineExpose({
	openDialog,
});
</script>

<style scoped>
:deep(.el-radio) {
	display: block;
	margin-bottom: 10px;
	height: auto;
	line-height: 1.5;
}

:deep(.el-radio__label) {
	white-space: normal;
	line-height: 1.5;
}
</style>
