# 皮试管理功能实现说明

## 功能概述

基于已有的后端皮试结果服务API，创建了一个完整的前端皮试管理页面，实现了皮试结果的录入、查询、撤销等核心功能。

## 文件结构

```
src/
├── api/outpatientDoctor/
│   └── skinTestResult.ts                    # 皮试结果API接口
└── views/outpatientDoctor/skinTestResult/
    └── index.vue                           # 皮试管理主页面
```

## 功能特性

### 1. 主从表格布局
- **上方表格**：显示需要皮试的处方列表
- **下方表格**：显示选中处方的药品明细
- 点击处方行自动加载对应的处方明细

### 2. 条件查询功能
- 处方时间范围查询
- 开单科室筛选
- 支持重置查询条件
- 分页显示处方列表

### 3. 皮试结果操作
- **录入皮试结果**：对未进行皮试的药品进行皮试结果录入
- **撤销皮试结果**：对已有皮试结果的药品进行撤销操作
- 操作按钮根据皮试状态动态显示

### 4. 录入皮试结果弹窗
- 显示患者和药品基本信息
- 皮试结果选择（阴性/阳性/可疑）
- 皮试时间、方法、剂量等详细信息录入
- 反应描述和备注信息
- 表单验证和提交功能

### 5. 撤销皮试结果弹窗
- 显示当前皮试结果信息
- 要求输入撤销原因
- 二次确认撤销操作
- 警告提示信息

## API接口

### skinTestResult.ts
```typescript
export const useSkinTestResultApi = () => {
    return {
        page: baseApi.page,              // 分页查询需要皮试的处方
        detail: baseApi.detail,          // 查看处方详细
        add: baseApi.add,                // 录入皮试结果
        batchAdd: (data: any) => ...,    // 批量录入皮试结果
        cancel: (data: any) => ...,      // 撤销皮试结果
        pageRecord: (data: any) => ...,  // 分页查询皮试结果记录
    }
}
```

## 页面组件

### 主要状态管理
- `state.prescriptionData`: 处方主表数据
- `state.detailData`: 处方明细数据
- `state.selectedPrescription`: 当前选中的处方
- `addFormData`: 录入皮试结果表单数据
- `cancelFormData`: 撤销皮试结果表单数据
- `currentDrugInfo`: 当前操作的药品信息

### 核心功能函数
- `queryPrescriptions()`: 查询处方列表
- `loadPrescriptionDetail()`: 加载处方明细
- `handleAddSkinTest()`: 打开录入皮试结果弹窗
- `handleCancelSkinTest()`: 打开撤销皮试结果弹窗
- `submitAddSkinTest()`: 提交录入皮试结果
- `submitCancelSkinTest()`: 提交撤销皮试结果

## 业务流程

### 录入皮试结果流程
1. 查询需要皮试的处方列表
2. 选择处方，查看处方明细
3. 对需要皮试且未录入结果的药品点击"录入皮试"
4. 在弹窗中填写皮试结果信息
5. 提交后自动刷新数据

### 撤销皮试结果流程
1. 在处方明细中找到已有皮试结果的药品
2. 点击"撤销皮试"按钮
3. 在弹窗中查看当前皮试结果信息
4. 输入撤销原因
5. 二次确认后提交撤销

## 数据字典

### 皮试结果枚举
- `1`: 阴性（绿色标签）
- `2`: 阳性（红色标签）
- `3`: 可疑（橙色标签）

### 皮试状态枚举
- `0`: 未开始
- `1`: 进行中（橙色标签）
- `2`: 已完成（绿色标签）

### 皮试结果状态枚举
- `1`: 有效
- `2`: 撤销

## 样式特性

- 响应式布局设计
- 卡片式组件布局
- 表格行点击高亮
- 状态标签颜色区分
- 弹窗信息展示美观
- 加载状态和错误处理

## 使用说明

1. **启动项目**：
   ```bash
   npm run dev
   ```

2. **访问页面**：
   导航到皮试管理页面（需要配置路由）

3. **基本操作**：
   - 设置查询条件，点击查询按钮
   - 点击处方行查看明细
   - 对需要皮试的药品进行录入或撤销操作

## 注意事项

1. 页面需要配置相应的路由才能访问
2. 需要确保后端API服务正常运行
3. 用户需要有相应的权限才能进行皮试结果操作
4. 撤销操作不可逆，需要谨慎操作
5. 录入皮试结果时，皮试结果和皮试时间为必填项

## 扩展功能

可以根据需要添加以下功能：
- 批量录入皮试结果
- 皮试结果统计报表
- 皮试结果打印功能
- 皮试结果导出功能
- 皮试提醒功能
