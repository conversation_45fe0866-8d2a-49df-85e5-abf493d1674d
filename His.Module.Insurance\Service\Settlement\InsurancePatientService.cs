using His.Module.Insurance.Const;
using His.Module.Insurance.Entity;
using His.Module.Insurance.Service.Settlement.Dto.Patient;
using His.Module.Insurance.Service.Settlement.Model.Patient;
using Newtonsoft.Json;

namespace His.Module.Insurance.Service.Settlement;
/// <summary>
/// 医保患者
/// </summary>
[ApiDescriptionSettings(InsuranceConst.GroupName, Order = 100)]
public class InsurancePatientService(
    IPatientApi patientApi,
    ICommonApi commonApi,
    SqlSugarRepository<PersonalInfo> personalInfoRepository
    ): IDynamicApiController, ITransient
{
    /// <summary>
    /// 获取医保结算用户key 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取医保结算用户key")]
    [ApiDescriptionSettings(Name = "GetUserKey"), HttpGet]
    public async Task<string> GetUserKey()
    {
        return await commonApi.GetUserKey();
    }
    
    /// <summary>
    /// 3.2.1获取人员基本信息（无卡）
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [DisplayName("3.2.1获取人员基本信息（无卡）")]
    [ApiDescriptionSettings(Name = "QueryBasicInfo"), HttpPost]
    public async Task<QueryBasicInfoResponse> QueryBasicInfo( QueryBasicInfoRequest request)
    {
     //   if(string.IsNullOrEmpty(request.UserKey))
        //    request.UserKey = await this.GetUserKey();
        // var request = new QueryBasicInfoRequest()
        // {
        //     UserKey =await this.GetUserKey(),
        //     p_grbh = input.IdCardNo,
        //     //MedicalCategory = input.MedicalCategory,
        //     p_xzbz = input.MedicalInsuranceFlag,
        //     p_xm = input.Name,
        //     p_yltclb = input.MedicalPoolingCategory
        // };

        
       // return await patientApi.QueryBasicInfo(request); 
        var mock = $@"{{
    ""gfbb"": ""规范版本"",
    ""mzdbbz"": ""门诊大病备注"",
    ""sfzhm"": ""370831199107053157"",
    ""bnyzftcj_de"": 0,
    ""yfdxbz"": ""0"",
    ""idtype"": """",
    ""zfsm"": ""支付说明（灰名单原因）"",
    ""xm"": ""王凯"",
     ""zccyrq"": ""2023-07-05"",
    ""appid"": ""1DBHJRS5I01E0F34A8C00000B959FA86"",
    ""resultcode"": 0,
    ""fpjzsf"": """",
    ""jbjgid"": ""37000000"",
    ""cbzt"": ""参保状态"",
    ""xb"": ""1"",
    ""resulttext"": ""调用成功"",
    ""mzddbz"": ""门诊定点标志值为1时，当前医院是该参保人的门诊统筹定点医院。（异地不返回）"",
    ""zfbz"": ""1"",
    ""dwmc"": ""单位名称	获取持卡人所属单位名称"",
    ""zcyymc"": ""*"",
    ""dsjgbh"": ""37000100"",
    ""ylzdjfny"": """",
    ""sbm"": ""37000100"",
    ""wxtoken"": """",
    ""zyzt"": """",
    ""lbjbbm"": ""居民两病备案疾病编码"",
    ""mzddsm"": ""门诊定点说明	参保人的门诊统筹定点医院说明 （异地不返回）"",
    ""sbjgbh"": ""37000000"",
    ""bcrylb"": ""补充人员类别。	该值用于判断参保人是否为补充医疗人员和保健人群(‘A‘ 补充医疗人员，’B’ 保健人群)，济南返回代码编号，H3地区返回具体汉字说明。"",
    ""dwbh"": """",
    ""tjrylbmx"": """",
    ""jsffbaxx"": """",
    ""mzdb_ejjbbm"": """",
    ""zhzybz"": ""0"",
    ""cbjgmc"": ""天桥区医疗保险事业中心"",
    ""kyhzh"": """",
    ""ydbz"": ""0"",
    ""insuplc_admdvs"": ""370100"",
    ""syzdjfny"": """",
    ""codetype"": ""001"",
    ""mzmxm_ejjbbm"": ""门慢的二级代码"",
    ""gscbzdyf"": """",
    ""mzdb_bz"": """",
    ""gsjcxx"": """",
    ""cbxz"": ""C"",
    ""zhzysm"": ""15(医保参数控制)天内的住院记录说"",
    ""ptmztcsm"": """",
    ""dwxzmc"": ""单位性质名称	获取无卡人单位性质名称"",
    ""mz"": """",
    ""mdtrt_cert_type"": ""01"",
    ""jsjg"": """",
    ""agreement_sts"": """",
    ""mzdbjbs"": """",
    ""zysm"": """",
    ""cbdsbh"": ""37000100"",//参保地市编号
    ""rydjid"": ""12370831199107053157"",
    ""bz"": """",
    ""mzmxm_ejjbmc"": ""门慢的二级名称"",
    ""signno"": """",
    ""esicard_no"": """",
    ""ectoken"": ""370000ec0eoq3nmh7945c7500a0000e16ce853"",
    ""posfzhm"": """",
    ""kh"": """",
    ""sbqbye"": 0,
    ""psamkh"": """",
    ""mzdbxesm"": """",
    ""ybbh"": """",
    ""jzrylb"": ""助人员类别"",
    ""dwxz"": ""单位性质"",
    ""multisbj"": ""0"",
    ""xzqh"": ""行政区划"",
    ""lbjbmc"": """",
    ""mzdb_bzs"": """",
    ""zhye"": 1621.16,
    ""gjzhye"": 0,
    ""ptmzjbs"": """",
    ""psn_no"": ""12370831199107053157"",
    ""channel_no"": ""02"",
    ""insutype"": ""310"",
    ""pkrkbz"": """",
    ""tjrylb"": """",
    ""scbwjcd"": """",
    ""bnyzftcj_jb"": 0,
    ""sfxsptmztc"": """",
    ""ylrylbcode"": ""0"",
    ""mzdb_ejjbmc"": """",
    ""ryid"": ""12370831199107053157"",
    ""ylrylb"": ""在职"",
    ""cqhlbalb"": """",
    ""ye"": 1621.16,
    ""sbkydbz"": ""1"",
    ""dyrylb"": ""0"",
    ""rqlb"": ""A"",
    ""dqsfsm"": """",
    ""poxm"": """",
    ""yfdxlb"": ""0"",
    ""dynamic_code"": ""4243941324666511462402370000"",
    ""qdzffs"": """",
    ""mzdbyys"": """",
    ""csrq"": ""19910705000000"",
    ""shbzhm"": ""370831199107053157"",
    ""mzddyy"": """",
    ""grbh"": ""370831199107053157""
}}";
        var mockData = JsonConvert.DeserializeObject<QueryBasicInfoResponse>(mock);
        return mockData; 
    }
 
    //保存医保人员信息 
    [DisplayName("保存人员基本信息")]
    [ApiDescriptionSettings(Name = "SavePersonalInfo"), HttpPost]
    public async Task<bool> SaveBasicInfo(SavePersonalInfoInput request)
    {
        var entity = request.QueryBasicInfoResponse != null ? 
            request.QueryBasicInfoResponse.Adapt<PersonalInfo>() : 
            request.ReadCardResponse.Adapt<PersonalInfo>();
        entity.RegisterId = request.RegisterId;
        entity.VisitNo = request.VisitNo;
        entity.PatientId = request.PatientId;
        entity.OutpatientNo = request.OutpatientNo;
        return  await personalInfoRepository.InsertAsync(entity);
    }
    
}