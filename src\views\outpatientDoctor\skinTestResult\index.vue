<script lang="ts" setup name="skinTestResult">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import { useSkinTestResultApi } from '/@/api/outpatientDoctor/skinTestResult';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import { formatDate } from '/@/utils/formatTime';

// API服务
const skinTestResultApi = useSkinTestResultApi();
const basicInfoApi = useBasicInfoApi();

// 弹窗状态
const addDialogVisible = ref(false);
const cancelDialogVisible = ref(false);
const dialogLoading = ref(false);

const state = reactive({
	tableLoading: false,
	detailLoading: false,
	// 查询参数
	queryParams: {
		prescriptionTimeRange: [] as string[],
		isSkinTest: 1, // 默认查询需要皮试的
		billingDeptId: undefined,
	},
	// 分页参数
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
	},
	// 处方主表数据
	prescriptionData: [] as any[],
	// 选中的处方
	selectedPrescription: null as any,
	// 处方明细数据
	detailData: [] as any[],
	// 科室列表
	orgList: [] as any[],
});

// 录入皮试结果表单数据
const addFormData = reactive({
	prescriptionDetailId: 0,
	skinTestResult: '',
	skinTestTime: '',
	skinTestMethod: '',
	skinTestDose: '',
	reactionDescription: '',
	remark: '',
});

// 撤销皮试结果表单数据
const cancelFormData = reactive({
	id: 0,
	cancelReason: '',
});

// 当前操作的药品信息
const currentDrugInfo = reactive({
	patientName: '',
	gender: '',
	age: '',
	ageUnit: '',
	prescriptionNo: '',
	drugName: '',
	drugSpec: '',
	quantity: '',
	unit: '',
	skinTestResult: '',
	skinTestTime: '',
	skinTestStaffName: '',
});

// 皮试结果选项
const skinTestResultOptions = [
	{ label: '阴性', value: '1' },
	{ label: '阳性', value: '2' },
	{ label: '可疑', value: '3' },
];

// 皮试状态选项
const skinTestStatusOptions = [
	{ label: '未开始', value: 0 },
	{ label: '进行中', value: 1 },
	{ label: '已完成', value: 2 },
];

// 页面加载时
onMounted(async () => {
	// 设置默认查询时间范围（今天）
	const today = new Date();
	const startOfDay = new Date(today);
	startOfDay.setHours(0, 0, 0, 0);
	const endOfDay = new Date(today);
	endOfDay.setHours(23, 59, 59, 999);

	state.queryParams.prescriptionTimeRange = [formatDate(startOfDay, 'YYYY-mm-dd HH:MM:SS'), formatDate(endOfDay, 'YYYY-mm-dd HH:MM:SS')];

	// 获取科室列表
	await loadOrgList();
	// 查询数据
	await queryPrescriptions();
});

// 获取科室列表
const loadOrgList = async () => {
	try {
		const res = await basicInfoApi.getDepartments({ orgTypes: ['701', '901'] });
		state.orgList = res.data.result ?? [];
	} catch (error) {
		console.error('获取科室列表失败:', error);
	}
};

// 查询处方列表
const queryPrescriptions = async () => {
	try {
		state.tableLoading = true;
		const params = {
			...state.tableParams,
			...state.queryParams,
		};
		const res = await skinTestResultApi.page(params);
		state.prescriptionData = res.data.result ?? [];
		state.tableParams.total = res.data.total ?? 0;
	} catch (error) {
		console.error('查询处方列表失败:', error);
		ElMessage.error('查询处方列表失败');
	} finally {
		state.tableLoading = false;
	}
};

// 重置查询条件
const resetQuery = () => {
	state.queryParams = {
		prescriptionTimeRange: [],
		isSkinTest: 1,
		billingDeptId: undefined,
	};
	state.tableParams.page = 1;
	queryPrescriptions();
};

// 处方行点击事件
const handlePrescriptionRowClick = async (row: any) => {
	state.selectedPrescription = row;
	await loadPrescriptionDetail(row.id);
};

// 加载处方明细
const loadPrescriptionDetail = async (prescriptionId: number) => {
	try {
		state.detailLoading = true;
		const res = await skinTestResultApi.detail({ id: prescriptionId });
		state.detailData = res.data.result ?? [];
	} catch (error) {
		console.error('加载处方明细失败:', error);
		ElMessage.error('加载处方明细失败');
	} finally {
		state.detailLoading = false;
	}
};

// 录入皮试结果
const handleAddSkinTest = (row: any) => {
	// 重置表单数据
	Object.assign(addFormData, {
		prescriptionDetailId: row.id,
		skinTestResult: '',
		skinTestTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss'),
		skinTestMethod: '',
		skinTestDose: '',
		reactionDescription: '',
		remark: '',
	});

	// 设置药品信息
	Object.assign(currentDrugInfo, {
		patientName: state.selectedPrescription?.patientName || '',
		gender: state.selectedPrescription?.gender || '',
		age: state.selectedPrescription?.age || '',
		ageUnit: state.selectedPrescription?.ageUnit || '',
		prescriptionNo: state.selectedPrescription?.prescriptionNo || '',
		drugName: row.drugName || '',
		drugSpec: row.drugSpec || '',
		quantity: row.quantity || '',
		unit: row.unit || '',
	});

	addDialogVisible.value = true;
};

// 撤销皮试结果
const handleCancelSkinTest = (row: any) => {
	// 重置表单数据
	Object.assign(cancelFormData, {
		id: row.skinTestResultId,
		cancelReason: '',
	});

	// 设置药品信息
	Object.assign(currentDrugInfo, {
		patientName: state.selectedPrescription?.patientName || '',
		prescriptionNo: state.selectedPrescription?.prescriptionNo || '',
		drugName: row.drugName || '',
		drugSpec: row.drugSpec || '',
		skinTestResult: getSkinTestResultText(row.skinTestResult),
		skinTestTime: row.skinTestTime || '',
		skinTestStaffName: row.skinTestStaffName || '',
	});

	cancelDialogVisible.value = true;
};

// 提交录入皮试结果
const submitAddSkinTest = async () => {
	if (!addFormData.skinTestResult) {
		ElMessage.error('请选择皮试结果');
		return;
	}
	if (!addFormData.skinTestTime) {
		ElMessage.error('请选择皮试时间');
		return;
	}

	try {
		dialogLoading.value = true;
		await skinTestResultApi.add(addFormData);
		ElMessage.success('皮试结果录入成功');
		addDialogVisible.value = false;
		handleAddSuccess();
	} catch (error) {
		console.error('录入皮试结果失败:', error);
		ElMessage.error('录入皮试结果失败');
	} finally {
		dialogLoading.value = false;
	}
};

// 提交撤销皮试结果
const submitCancelSkinTest = async () => {
	if (!cancelFormData.cancelReason.trim()) {
		ElMessage.error('请输入撤销原因');
		return;
	}

	try {
		await ElMessageBox.confirm('撤销后将清空该药品的皮试结果，确定要撤销吗？', '确认撤销', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		dialogLoading.value = true;
		await skinTestResultApi.cancel(cancelFormData);
		ElMessage.success('皮试结果撤销成功');
		cancelDialogVisible.value = false;
		handleCancelSuccess();
	} catch (error) {
		if (error !== 'cancel') {
			console.error('撤销皮试结果失败:', error);
			ElMessage.error('撤销皮试结果失败');
		}
	} finally {
		dialogLoading.value = false;
	}
};

// 皮试结果录入成功回调
const handleAddSuccess = () => {
	// 刷新处方列表和明细
	queryPrescriptions();
	if (state.selectedPrescription) {
		loadPrescriptionDetail(state.selectedPrescription.id);
	}
};

// 皮试结果撤销成功回调
const handleCancelSuccess = () => {
	// 刷新处方列表和明细
	queryPrescriptions();
	if (state.selectedPrescription) {
		loadPrescriptionDetail(state.selectedPrescription.id);
	}
};

// 分页变化
const handlePageChange = (page: number) => {
	state.tableParams.page = page;
	queryPrescriptions();
};

const handleSizeChange = (size: number) => {
	state.tableParams.pageSize = size;
	state.tableParams.page = 1;
	queryPrescriptions();
};

// 获取皮试结果文本
const getSkinTestResultText = (value: string) => {
	const option = skinTestResultOptions.find((item) => item.value === value);
	return option?.label || value;
};

// 获取皮试状态文本
const getSkinTestStatusText = (value: number) => {
	const option = skinTestStatusOptions.find((item) => item.value === value);
	return option?.label || value;
};

// 获取皮试状态标签类型
const getSkinTestStatusType = (value: number) => {
	switch (value) {
		case 0:
			return '';
		case 1:
			return 'warning';
		case 2:
			return 'success';
		default:
			return '';
	}
};
</script>

<template>
	<div class="skin-test-result-container">
		<!-- 查询条件 -->
		<el-card class="box-card" shadow="hover">
			<template #header>
				<div class="card-header">
					<span>查询条件</span>
				</div>
			</template>
			<el-form :model="state.queryParams" label-width="100px" inline>
				<el-form-item label="处方时间">
					<el-date-picker
						v-model="state.queryParams.prescriptionTimeRange"
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						style="width: 350px"
					/>
				</el-form-item>
				<el-form-item label="开单科室">
					<el-select v-model="state.queryParams.billingDeptId" placeholder="请选择科室" clearable style="width: 200px">
						<el-option v-for="org in state.orgList" :key="org.id" :label="org.name" :value="org.id" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="queryPrescriptions">
						<el-icon><Search /></el-icon>
						查询
					</el-button>
					<el-button @click="resetQuery">
						<el-icon><Refresh /></el-icon>
						重置
					</el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<!-- 处方主表 -->
		<el-card class="box-card" shadow="hover" style="margin-top: 10px">
			<template #header>
				<div class="card-header">
					<span>需要皮试的处方列表</span>
				</div>
			</template>
			<el-table :data="state.prescriptionData" v-loading="state.tableLoading" highlight-current-row @row-click="handlePrescriptionRowClick" style="width: 100%">
				<el-table-column prop="prescriptionNo" label="处方号" width="150" />
				<el-table-column prop="prescriptionTime" label="处方时间" width="180" />
				<el-table-column prop="patientName" label="患者姓名" width="100" />
				<el-table-column label="性别年龄" width="100">
					<template #default="{ row }"> {{ row.gender }} {{ row.age }}{{ row.ageUnit }} </template>
				</el-table-column>
				<el-table-column prop="billingDeptName" label="开单科室" width="120" />
				<el-table-column prop="billingDoctorName" label="开单医生" width="100" />
				<el-table-column label="皮试进度" width="120">
					<template #default="{ row }"> {{ row.completedSkinTestCount }}/{{ row.skinTestDrugCount }} </template>
				</el-table-column>
				<el-table-column label="皮试状态" width="100">
					<template #default="{ row }">
						<el-tag :type="getSkinTestStatusType(row.skinTestStatus)">
							{{ getSkinTestStatusText(row.skinTestStatus) }}
						</el-tag>
					</template>
				</el-table-column>
			</el-table>

			<!-- 分页 -->
			<div class="pagination-container">
				<el-pagination
					v-model:current-page="state.tableParams.page"
					v-model:page-size="state.tableParams.pageSize"
					:page-sizes="[10, 20, 50, 100]"
					:total="state.tableParams.total"
					layout="total, sizes, prev, pager, next, jumper"
					@size-change="handleSizeChange"
					@current-change="handlePageChange"
				/>
			</div>
		</el-card>

		<!-- 处方明细表 -->
		<el-card class="box-card" shadow="hover" style="margin-top: 10px" v-if="state.selectedPrescription">
			<template #header>
				<div class="card-header">
					<span>处方明细 - {{ state.selectedPrescription.prescriptionNo }}</span>
					<span style="margin-left: 20px; font-size: 14px; color: #666">
						患者：{{ state.selectedPrescription.patientName }}
						{{ state.selectedPrescription.gender }}
						{{ state.selectedPrescription.age }}{{ state.selectedPrescription.ageUnit }}
					</span>
				</div>
			</template>
			<el-table :data="state.detailData" v-loading="state.detailLoading" style="width: 100%">
				<el-table-column prop="drugName" label="药品名称" width="200" />
				<el-table-column prop="drugSpec" label="规格" width="120" />
				<el-table-column label="数量" width="100">
					<template #default="{ row }"> {{ row.quantity }}{{ row.unit }} </template>
				</el-table-column>
				<el-table-column label="是否需要皮试" width="120">
					<template #default="{ row }">
						<el-tag :type="row.isSkinTest === 1 ? 'danger' : ''">
							{{ row.isSkinTest === 1 ? '需要皮试' : '无需皮试' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="皮试结果" width="100">
					<template #default="{ row }">
						<el-tag v-if="row.skinTestResult" :type="row.skinTestResult === '1' ? 'success' : row.skinTestResult === '2' ? 'danger' : 'warning'">
							{{ getSkinTestResultText(row.skinTestResult) }}
						</el-tag>
						<span v-else style="color: #999">未录入</span>
					</template>
				</el-table-column>
				<el-table-column prop="skinTestTime" label="皮试时间" width="180" />
				<el-table-column prop="skinTestStaffName" label="皮试人员" width="100" />
				<el-table-column prop="skinTestMethod" label="皮试方法" width="120" />
				<el-table-column prop="reactionDescription" label="反应描述" width="150" show-overflow-tooltip />
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="{ row }">
						<template v-if="row.isSkinTest === 1">
							<el-button v-if="!row.skinTestResult || row.skinTestResultStatus === 2" type="primary" size="small" @click="handleAddSkinTest(row)"> 录入皮试 </el-button>
							<el-button v-if="row.skinTestResult && row.skinTestResultStatus === 1" type="warning" size="small" @click="handleCancelSkinTest(row)"> 撤销皮试 </el-button>
						</template>
						<span v-else style="color: #999">无需操作</span>
					</template>
				</el-table-column>
			</el-table>
		</el-card>

		<!-- 录入皮试结果弹窗 -->
		<el-dialog v-model="addDialogVisible" title="录入皮试结果" width="600px" :close-on-click-modal="false">
			<!-- 患者和药品信息 -->
			<el-card class="info-card" shadow="never">
				<template #header>
					<span style="font-weight: bold; color: #409eff">患者和药品信息</span>
				</template>
				<el-descriptions :column="2" border>
					<el-descriptions-item label="患者姓名">{{ currentDrugInfo.patientName }}</el-descriptions-item>
					<el-descriptions-item label="性别年龄">{{ currentDrugInfo.gender }} {{ currentDrugInfo.age }}{{ currentDrugInfo.ageUnit }}</el-descriptions-item>
					<el-descriptions-item label="处方号">{{ currentDrugInfo.prescriptionNo }}</el-descriptions-item>
					<el-descriptions-item label="药品名称">{{ currentDrugInfo.drugName }}</el-descriptions-item>
					<el-descriptions-item label="药品规格">{{ currentDrugInfo.drugSpec }}</el-descriptions-item>
					<el-descriptions-item label="数量">{{ currentDrugInfo.quantity }}{{ currentDrugInfo.unit }}</el-descriptions-item>
				</el-descriptions>
			</el-card>

			<!-- 皮试结果表单 -->
			<el-form label-width="100px" style="margin-top: 20px">
				<el-form-item label="皮试结果" required>
					<el-radio-group v-model="addFormData.skinTestResult">
						<el-radio v-for="option in skinTestResultOptions" :key="option.value" :label="option.value" :value="option.value">
							{{ option.label }}
						</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="皮试时间" required>
					<el-date-picker v-model="addFormData.skinTestTime" type="datetime" placeholder="选择皮试时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
				</el-form-item>

				<el-form-item label="皮试方法">
					<el-input v-model="addFormData.skinTestMethod" placeholder="请输入皮试方法" maxlength="100" show-word-limit />
				</el-form-item>

				<el-form-item label="皮试剂量">
					<el-input v-model="addFormData.skinTestDose" placeholder="请输入皮试剂量" maxlength="50" show-word-limit />
				</el-form-item>

				<el-form-item label="反应描述">
					<el-input v-model="addFormData.reactionDescription" type="textarea" :rows="3" placeholder="请输入反应描述" maxlength="1000" show-word-limit />
				</el-form-item>

				<el-form-item label="备注">
					<el-input v-model="addFormData.remark" type="textarea" :rows="2" placeholder="请输入备注" maxlength="500" show-word-limit />
				</el-form-item>
			</el-form>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="addDialogVisible = false">取消</el-button>
					<el-button type="primary" :loading="dialogLoading" @click="submitAddSkinTest"> 确定 </el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 撤销皮试结果弹窗 -->
		<el-dialog v-model="cancelDialogVisible" title="撤销皮试结果" width="600px" :close-on-click-modal="false">
			<!-- 皮试结果信息 -->
			<el-card class="info-card" shadow="never">
				<template #header>
					<span style="font-weight: bold; color: #e6a23c">当前皮试结果信息</span>
				</template>
				<el-descriptions :column="2" border>
					<el-descriptions-item label="患者姓名">{{ currentDrugInfo.patientName }}</el-descriptions-item>
					<el-descriptions-item label="处方号">{{ currentDrugInfo.prescriptionNo }}</el-descriptions-item>
					<el-descriptions-item label="药品名称">{{ currentDrugInfo.drugName }}</el-descriptions-item>
					<el-descriptions-item label="药品规格">{{ currentDrugInfo.drugSpec }}</el-descriptions-item>
					<el-descriptions-item label="皮试结果">
						<el-tag :type="currentDrugInfo.skinTestResult === '阴性' ? 'success' : currentDrugInfo.skinTestResult === '阳性' ? 'danger' : 'warning'">
							{{ currentDrugInfo.skinTestResult }}
						</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="皮试时间">{{ currentDrugInfo.skinTestTime }}</el-descriptions-item>
					<el-descriptions-item label="皮试人员">{{ currentDrugInfo.skinTestStaffName }}</el-descriptions-item>
				</el-descriptions>
			</el-card>

			<!-- 撤销原因表单 -->
			<el-form label-width="100px" style="margin-top: 20px">
				<el-form-item label="撤销原因" required>
					<el-input v-model="cancelFormData.cancelReason" type="textarea" :rows="4" placeholder="请输入撤销原因（必填）" maxlength="200" show-word-limit />
				</el-form-item>
			</el-form>

			<!-- 警告提示 -->
			<el-alert title="注意：撤销后将清空该药品的皮试结果，如需重新录入请点击录入皮试按钮" type="warning" :closable="false" style="margin-top: 20px" />

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="cancelDialogVisible = false">取消</el-button>
					<el-button type="danger" :loading="dialogLoading" @click="submitCancelSkinTest"> 确定撤销 </el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<style scoped>
.skin-test-result-container {
	padding: 10px;
}

.box-card {
	margin-bottom: 10px;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: bold;
}

.pagination-container {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}

:deep(.el-table__row) {
	cursor: pointer;
}

:deep(.el-table__row:hover) {
	background-color: #f5f7fa;
}

:deep(.current-row) {
	background-color: #ecf5ff;
}
</style>
