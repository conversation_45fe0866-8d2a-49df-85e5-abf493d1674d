<script lang="ts" setup name="skinTestResult">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import { useSkinTestResultApi } from '/@/api/outpatientDoctor/skinTestResult';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import { formatDate } from '/@/utils/formatTime';
import AddSkinTestDialog from '/@/views/outpatientDoctor/skinTestResult/component/addSkinTestDialog.vue';
import CancelSkinTestDialog from '/@/views/outpatientDoctor/skinTestResult/component/cancelSkinTestDialog.vue';

// API服务
const skinTestResultApi = useSkinTestResultApi();
const basicInfoApi = useBasicInfoApi();

// 组件引用
const addSkinTestDialogRef = ref();
const cancelSkinTestDialogRef = ref();

const state = reactive({
	tableLoading: false,
	detailLoading: false,
	// 查询参数
	queryParams: {
		prescriptionTimeRange: [] as string[],
		isSkinTest: undefined, // 默认查询需要皮试的
		billingDeptId: undefined,
	},
	// 分页参数
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
	},
	// 处方主表数据
	prescriptionData: [] as any[],
	// 选中的处方
	selectedPrescription: null as any,
	// 处方明细数据
	detailData: [] as any[],
	// 科室列表
	orgList: [] as any[],
});

// 皮试结果选项
const skinTestResultOptions = [
	{ label: '阴性', value: '1' },
	{ label: '阳性', value: '2' },
	{ label: '可疑', value: '3' },
];

// 皮试状态选项
const skinTestStatusOptions = [
	{ label: '未开始', value: 0 },
	{ label: '进行中', value: 1 },
	{ label: '已完成', value: 2 },
];

// 页面加载时
onMounted(async () => {
	// 设置默认查询时间范围（今天）
	const today = new Date();
	const startOfDay = new Date(today);
	startOfDay.setHours(0, 0, 0, 0);
	const endOfDay = new Date(today);
	endOfDay.setHours(23, 59, 59, 999);

	state.queryParams.prescriptionTimeRange = [formatDate(startOfDay, 'YYYY-mm-dd HH:MM:SS'), formatDate(endOfDay, 'YYYY-mm-dd HH:MM:SS')];

	// 获取科室列表
	await loadOrgList();
	// 查询数据
	await queryPrescriptions();
});

// 获取科室列表
const loadOrgList = async () => {
	try {
		const res = await basicInfoApi.getDepartments({ orgTypes: ['701', '901'] });
		state.orgList = res.data.result ?? [];
	} catch (error) {
		console.error('获取科室列表失败:', error);
	}
};

// 查询处方列表
const queryPrescriptions = async () => {
	try {
		state.tableLoading = true;
		const params = {
			...state.tableParams,
			...state.queryParams,
		};
		const res = await skinTestResultApi.page(params);
		state.prescriptionData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total ?? 0;
	} catch (error) {
		console.error('查询处方列表失败:', error);
		ElMessage.error('查询处方列表失败');
	} finally {
		state.tableLoading = false;
	}
};

// 重置查询条件
const resetQuery = () => {
	state.queryParams = {
		prescriptionTimeRange: [],
		isSkinTest: undefined,
		billingDeptId: undefined,
	};
	state.tableParams.page = 1;
	queryPrescriptions();
};

// 处方行点击事件
const handlePrescriptionRowClick = async (row: any) => {
	state.selectedPrescription = row;
	await loadPrescriptionDetail(row.id);
};

// 加载处方明细
const loadPrescriptionDetail = async (prescriptionId: number) => {
	try {
		state.detailLoading = true;
		const res = await skinTestResultApi.detail({ id: prescriptionId });
		state.detailData = res.data.result ?? [];
	} catch (error) {
		console.error('加载处方明细失败:', error);
		ElMessage.error('加载处方明细失败');
	} finally {
		state.detailLoading = false;
	}
};

// 录入皮试结果
const handleAddSkinTest = (row: any) => {
	addSkinTestDialogRef.value?.open(row, state.selectedPrescription);
};

// 撤销皮试结果
const handleCancelSkinTest = (row: any) => {
	cancelSkinTestDialogRef.value?.open(row, state.selectedPrescription);
};

// 皮试结果录入成功回调
const handleAddSuccess = () => {
	// 刷新处方列表和明细
	queryPrescriptions();
	if (state.selectedPrescription) {
		loadPrescriptionDetail(state.selectedPrescription.id);
	}
};

// 皮试结果撤销成功回调
const handleCancelSuccess = () => {
	// 刷新处方列表和明细
	queryPrescriptions();
	if (state.selectedPrescription) {
		loadPrescriptionDetail(state.selectedPrescription.id);
	}
};

// 分页变化
const handlePageChange = (page: number) => {
	state.tableParams.page = page;
	queryPrescriptions();
};

const handleSizeChange = (size: number) => {
	state.tableParams.pageSize = size;
	state.tableParams.page = 1;
	queryPrescriptions();
};

// 获取皮试结果文本
const getSkinTestResultText = (value: string) => {
	const option = skinTestResultOptions.find((item) => item.value === value);
	return option?.label || value;
};

// 获取皮试状态文本
const getSkinTestStatusText = (value: number) => {
	const option = skinTestStatusOptions.find((item) => item.value === value);
	return option?.label || value;
};

// 获取皮试状态标签类型
const getSkinTestStatusType = (value: number) => {
	switch (value) {
		case 0:
			return '';
		case 1:
			return 'warning';
		case 2:
			return 'success';
		default:
			return '';
	}
};

// 获取处方状态文本
const getPrescriptionStatusText = (value: number) => {
	switch (value) {
		case 0:
			return '未审核';
		case 1:
			return '未收费';
		case 2:
			return '已收费';
		case 3:
			return '已取药';
		case 4:
			return '已退药';
		case 5:
			return '已退费';
		case 6:
			return '作废';
		default:
			return '未知';
	}
};

// 获取处方状态类型
const getPrescriptionStatusType = (value: number) => {
	switch (value) {
		case 0:
			return 'info';
		case 1:
			return 'warning';
		case 2:
			return 'primary';
		case 3:
			return 'success';
		case 4:
		case 5:
		case 6:
			return 'danger';
		default:
			return '';
	}
};
</script>

<template>
	<div class="skin-test-result-container">
		<!-- 查询条件 -->
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.queryParams" labelWidth="80" :inline="true">
				<el-form-item label="处方时间">
					<el-date-picker
						v-model="state.queryParams.prescriptionTimeRange"
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						style="width: 350px"
					/>
				</el-form-item>
				<el-form-item label="开单科室">
					<el-select v-model="state.queryParams.billingDeptId" placeholder="请选择科室" clearable style="width: 200px">
						<el-option v-for="org in state.orgList" :key="org.id" :label="org.name" :value="org.id" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="queryPrescriptions">
						<el-icon><Search /></el-icon>
						查询
					</el-button>
					<el-button @click="resetQuery">
						<el-icon><Refresh /></el-icon>
						重置
					</el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<!-- 处方主表 -->
		<el-card el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="state.prescriptionData" v-loading="state.tableLoading" highlight-current-row @row-click="handlePrescriptionRowClick" style="width: 100%">
				<el-table-column prop="patientName" label="患者姓名" width="120" />
				<el-table-column prop="visitNo" label="就诊号" width="150" />
				<el-table-column prop="prescriptionNo" label="处方号" width="150" />
				<el-table-column prop="prescriptionTime" label="处方时间" width="180">

				</el-table-column>
				<el-table-column prop="status" label="状态" width="100">
					<template #default="scope">
						<el-tag :type="getPrescriptionStatusType(scope.row.status)">
							{{ getPrescriptionStatusText(scope.row.status) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="diagnosticName" label="诊断名称" width="200" show-overflow-tooltip />
				<el-table-column prop="billingDeptName" label="开单科室" width="120" />
				<el-table-column prop="billingDoctorName" label="开单医生" width="120" />
			</el-table>

			<!-- 分页 -->
			<div class="pagination-container">
				<el-pagination
					v-model:current-page="state.tableParams.page"
					v-model:page-size="state.tableParams.pageSize"
					:page-sizes="[10, 20, 50, 100]"
					:total="state.tableParams.total"
					layout="total, sizes, prev, pager, next, jumper"
					@size-change="handleSizeChange"
					@current-change="handlePageChange"
				/>
			</div>
		</el-card>

		<!-- 处方明细表 -->
		<el-card el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<template #header>
				<div class="card-header">
					<span>处方明细 - 过敏药品</span>
				</div>
			</template>
			<el-table :data="state.detailData" v-loading="state.detailLoading" style="width: 100%">
				<el-table-column prop="drugName" label="药品名称" width="200" />
				<el-table-column prop="drugSpec" label="规格" width="120" />
				<el-table-column label="数量" width="100">
					<template #default="{ row }"> {{ row.quantity }}{{ row.unit }} </template>
				</el-table-column>
				<el-table-column label="是否需要皮试" width="120">
					<template #default="{ row }">
						<el-tag :type="row.isSkinTest === 1 ? 'danger' : ''">
							{{ row.isSkinTest === 1 ? '需要皮试' : '无需皮试' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="皮试结果" width="100">
					<template #default="{ row }">
						<el-tag v-if="row.skinTestResult" :type="row.skinTestResult === '1' ? 'success' : row.skinTestResult === '2' ? 'danger' : 'warning'">
							{{ getSkinTestResultText(row.skinTestResult) }}
						</el-tag>
						<span v-else style="color: #999">未录入</span>
					</template>
				</el-table-column>
				<el-table-column prop="skinTestTime" label="皮试时间" width="180" />
				<el-table-column prop="skinTestStaffName" label="皮试人员" width="100" />
				<el-table-column prop="skinTestMethod" label="皮试方法" width="120" />
				<el-table-column prop="reactionDescription" label="反应描述" width="150" show-overflow-tooltip />
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="{ row }">
						<template v-if="row.isSkinTest === 1">
							<el-button v-if="!row.skinTestResult || row.skinTestResultStatus === 2" type="primary" size="small" @click="handleAddSkinTest(row)"> 录入皮试 </el-button>
							<el-button v-if="row.skinTestResult && row.skinTestResultStatus === 1" type="warning" size="small" @click="handleCancelSkinTest(row)"> 撤销皮试 </el-button>
						</template>
						<span v-else style="color: #999">无需操作</span>
					</template>
				</el-table-column>
			</el-table>
		</el-card>

		<!-- 弹窗组件 -->
		<AddSkinTestDialog ref="addSkinTestDialogRef" @success="handleAddSuccess" />
		<CancelSkinTestDialog ref="cancelSkinTestDialogRef" @success="handleCancelSuccess" />
	</div>
</template>

<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
