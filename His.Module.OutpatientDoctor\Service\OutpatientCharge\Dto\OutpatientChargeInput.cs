﻿namespace His.Module.OutpatientDoctor.Service.OutpatientCharge.Dto;

public class OutpatientChargeInput
{

}

public class PageOutpatientChargeInput : BasePageInput
{

    /// <summary>
    /// 门诊流水号
    /// </summary>
    public string VisitNo { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    public string OutpatientNo { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string PatientName { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 收费时间范围
    /// </summary>
    public DateTime?[] ChargeTimeRange { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
}

/// <summary>
/// 未收费项目查询输入参数
/// </summary>
public class GetItemsInput
{
    /// <summary>
    /// 就诊ID
    /// </summary>
    public long? VisitId { get; set; }

    /// <summary>
    /// 门诊流水号
    /// </summary>
    public string? VisitNo { get; set; }


    /// <summary>
    /// 业务类型（lab_test/examination/prescription/dispose）
    /// </summary>
    public string? BusinessType { get; set; }


    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
}

public class ChargeOutpatientChargeInput
{
    /// <summary>
    /// 业务信息
    /// </summary>
    [Required(ErrorMessage = "业务信息不能为空")]
    public List<QueryByIdOutpatientChargeInput> BusinessDetails { get; set; }

    /// <summary>
    /// 门诊流水号
    /// </summary>
    [Required(ErrorMessage = "门诊流水号不能为空")]
    public string VisitNo { get; set; }
}

public class QueryByIdOutpatientChargeInput : BaseIdInput
{
    /// <summary>
    /// 申请单类型
    /// </summary>
    [Required(ErrorMessage = "业务类型不能为空")]
    public string BusinessType { get; set; }
}

