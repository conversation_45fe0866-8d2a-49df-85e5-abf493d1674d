﻿namespace His.Module.OutpatientDoctor.Service.OutpatientCharge.Dto;

public class OutpatientChargeOutput : ChargeMain
{
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>

    public int Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    public string AgeUnit { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>

    public string IdCardNo { get; set; }

    /// <summary>
    /// 费别名称
    /// </summary>
    public string FeeName { get; set; }

    /// <summary>
    /// 号别名称
    /// </summary>
    public string RegCategoryName { get; set; }

    /// <summary>
    /// 收费明细列表，包含与本次收费相关的所有详细条目。
    /// </summary>

    public List<OutpatientChargeDetailDto> Details { get; set; }
}

public class OutpatientChargeDetailDto : ChargeDetail
{
    /// <summary>
    /// 收费类别名称
    /// </summary>

    public string ChargeCategoryName { get; set; }

    /// <summary>
    /// 支付方式1名称
    /// </summary>
    public string PayMethod1Name { get; set; }

    /// <summary>
    /// 支付方式2名称
    /// </summary>
    public string PayMethod2Name { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    public string BillingDeptName { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    public string ExecuteDeptName { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    public int? IsPackage { get; set; }

    /// <summary>
    /// 套餐项目
    /// </summary>
    public List<PackageSubItemDto> PackageItems { get; set; }
}

/// <summary>
/// 未收费项目查询结果
/// </summary>
public class ItemsOutput
{
    /// <summary>
    /// 就诊ID
    /// </summary>
    public long? VisitId { get; set; }

    /// <summary>
    /// 门诊流水号
    /// </summary>
    public string VisitNo { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    public long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string PatientName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    public string? AgeUnit { get; set; }

    /// <summary>
    /// 检验模块项目
    /// </summary>
    public List<ModuleItemsDto> LabTestItems { get; set; } = [];

    /// <summary>
    /// 检查模块项目
    /// </summary>
    public List<ModuleItemsDto> ExaminationItems { get; set; } = [];

    /// <summary>
    /// 处方模块项目
    /// </summary>
    public List<ModuleItemsDto> PrescriptionItems { get; set; } = [];

    /// <summary>
    /// 处置模块项目
    /// </summary>
    public List<ModuleItemsDto> DisposeItems { get; set; } = [];

    /// <summary>
    /// 所有项目
    /// </summary>
    public List<ModuleItemsDto> SummaryItems { get; set; } = [];

    /// <summary>
    /// 汇总信息
    /// </summary>
    public SummaryDto Summary { get; set; } = new SummaryDto();
}

/// <summary>
/// 模块未收费项目DTO
/// </summary>
public class ModuleItemsDto
{
    /// <summary>
    /// 主表ID
    /// </summary>
    public long MainId { get; set; }

    /// <summary>
    /// 申请单号/处方号
    /// </summary>
    public string ApplyNo { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public string BusinessType { get; set; }
    /// <summary>
    /// 业务名称
    /// </summary>

    public string BusinessName { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    public DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室ID
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生ID
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    public string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行科室ID
    /// </summary>
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusDesc { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 明细项目列表
    /// </summary>
    public List<UnchargedItemDetailDto> Details { get; set; } = [];

    /// <summary>
    /// 该申请单总金额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 该申请单项目总数
    /// </summary>
    public int TotalCount { get; set; }
}

/// <summary>
/// 未收费项目明细DTO
/// </summary>
public class UnchargedItemDetailDto
{
    /// <summary>
    /// 收费类别名称
    /// </summary>
    public string? ChargeCategoryName { get; set; }

    /// <summary>
    /// 明细ID
    /// </summary>
    public long? DetailId { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// 执行科室ID
    /// </summary>
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>
    public string? NationalstandardCode { get; set; }

    /// <summary>
    /// 收费类别ID
    /// </summary>
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 是否套餐项目
    /// </summary>
    public int? IsPackage { get; set; }


    /// <summary>
    /// 自付比例
    /// </summary>
    public decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 套餐子项目列表（如果是套餐项目）
    /// </summary>
    public List<PackageSubItemDto>? PackageItems { get; set; }
}

/// <summary>
/// 套餐子项目DTO
/// </summary>
public class PackageSubItemDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>
    public string? NationalstandardCode { get; set; }

    /// <summary>
    /// 收费类别ID
    /// </summary>
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    public decimal? SelfPayRatio { get; set; }
}

/// <summary>
/// 未收费项目汇总DTO
/// </summary>
public class SummaryDto
{
    /// <summary>
    /// 检验项目总数
    /// </summary>
    public decimal LabTestCount { get; set; }

    /// <summary>
    /// 检验项目总金额
    /// </summary>
    public decimal LabTestAmount { get; set; }

    /// <summary>
    /// 检查项目总数
    /// </summary>
    public decimal ExaminationCount { get; set; }

    /// <summary>
    /// 检查项目总金额
    /// </summary>
    public decimal ExaminationAmount { get; set; }

    /// <summary>
    /// 处方项目总数
    /// </summary>
    public decimal PrescriptionCount { get; set; }

    /// <summary>
    /// 处方项目总金额
    /// </summary>
    public decimal PrescriptionAmount { get; set; }

    /// <summary>
    /// 处置项目总数
    /// </summary>
    public decimal DisposeCount { get; set; }

    /// <summary>
    /// 处置项目总金额
    /// </summary>
    public decimal DisposeAmount { get; set; }

    /// <summary>
    /// 总项目数
    /// </summary>
    public decimal TotalCount { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount { get; set; }
}