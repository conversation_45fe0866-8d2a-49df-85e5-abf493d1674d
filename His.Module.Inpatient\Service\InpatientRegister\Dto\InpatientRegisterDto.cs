﻿namespace His.Module.Inpatient;

/// <summary>
/// 住院登记输出参数
/// </summary>
public class InpatientRegisterDto
{
    /// <summary>
    /// 患者ID
    /// </summary>
    public string PatientIdFkColumn { get; set; }
    
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者编号
    /// </summary>
    public string? PatientNo { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    
 
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public CardTypeEnum IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 就诊卡ID
    /// </summary>
    public long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院次数
    /// </summary>
    public int? InpatientTimes { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    public string? MedicalRecordNo { get; set; }
    
 
    /// <summary>
    /// 费别Id
    /// </summary>
    public    long? FeeId { get; set; }
    /// <summary>
    /// 费别
    /// </summary>
  
    public    string? FeeName { get; set; }

    /// <summary>
    /// 医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 主治医生ID
    /// </summary>
    public long? MainDoctorId { get; set; }
    
    /// <summary>
    /// 主治医生姓名
    /// </summary>
    public string? MainDoctorName { get; set; }
    
    /// <summary>
    /// 入院诊断代码
    /// </summary>
    public string? InpatientDiagnosticCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public string? InpatientDiagnosticName { get; set; }
    
    /// <summary>
    /// 次要诊断代码
    /// </summary>
    public string? SecondaryDiagnosticCode { get; set; }
    
    /// <summary>
    /// 次要诊断名称
    /// </summary>
    public string? SecondaryDiagnosticName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>
    public DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 是否允许欠费
    /// </summary>
    public bool? AllowArrears { get; set; }
    
    /// <summary>
    /// 欠费上限
    /// </summary>
    public decimal? ArrearsLimit { get; set; }
    
    /// <summary>
    /// 担保人
    /// </summary>
    public string? GuaranteePerson { get; set; }
    
    /// <summary>
    /// 新生儿出生体重g
    /// </summary>
    public int? NewbornBirthWeight1 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    public int? NewbornBirthWeight2 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    public int? NewbornBirthWeight3 { get; set; }
    
    /// <summary>
    /// 新生儿入院体重g
    /// </summary>
    public int? NewbornInpatientWeight { get; set; }
    
    /// <summary>
    /// 是否有医保卡
    /// </summary>
    public int? HasMedicalInsurance { get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>

    public string ContactName { get; set; }

    // 联系电话

    public string ContactPhone { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>

    public string ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地区
    /// </summary>
    public List<String> ContactAddress { get; set; }


    /// <summary>
    /// 联系地址
    /// </summary>
    public string ContactAddressStreet { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 创建组织ID
    /// </summary>
    public long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建组织名称
    /// </summary>
    public string? CreateOrgName { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
