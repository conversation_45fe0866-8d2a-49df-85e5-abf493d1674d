# 处方主表列调整说明

## 调整概述

根据后端 `SkinTestResultOutput` 类的属性顺序，调整了皮试管理页面中处方主表的列显示，确保前端表格列与后端数据模型保持一致。

## 后端数据模型

```csharp
public class SkinTestResultOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }

    /// <summary>
    /// 处方号
    /// </summary>
    public string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    public DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosticName { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生姓名
    /// </summary>
    public string? BillingDoctorName { get; set; }
}
```

## 调整前后对比

### 调整前
```vue
<el-table-column prop="prescriptionNo" label="处方号" width="150" />
<el-table-column prop="prescriptionTime" label="处方时间" width="180" />
<el-table-column prop="patientName" label="患者姓名" width="100" />
```

### 调整后
```vue
<el-table-column prop="patientName" label="患者姓名" width="120" />
<el-table-column prop="visitNo" label="就诊号" width="150" />
<el-table-column prop="prescriptionNo" label="处方号" width="150" />
<el-table-column prop="prescriptionTime" label="处方时间" width="180">
    <template #default="scope">
        {{ formatDate(scope.row.prescriptionTime, 'YYYY-MM-DD HH:mm:ss') }}
    </template>
</el-table-column>
<el-table-column prop="status" label="状态" width="100">
    <template #default="scope">
        <el-tag :type="getPrescriptionStatusType(scope.row.status)">
            {{ getPrescriptionStatusText(scope.row.status) }}
        </el-tag>
    </template>
</el-table-column>
<el-table-column prop="diagnosticName" label="诊断名称" width="200" show-overflow-tooltip />
<el-table-column prop="billingDeptName" label="开单科室" width="120" />
<el-table-column prop="billingDoctorName" label="开单医生" width="120" />
```

## 主要调整内容

### 1. 列顺序调整
按照后端 `SkinTestResultOutput` 类的属性顺序重新排列表格列：
1. **患者姓名** (PatientName)
2. **就诊号** (VisitNo) - 新增
3. **处方号** (PrescriptionNo)
4. **处方时间** (PrescriptionTime)
5. **状态** (Status) - 新增
6. **诊断名称** (DiagnosticName) - 新增
7. **开单科室** (BillingDeptName) - 新增
8. **开单医生** (BillingDoctorName) - 新增

### 2. 新增列
- **就诊号**：显示患者的就诊号信息
- **状态**：显示处方状态，使用标签样式区分不同状态
- **诊断名称**：显示诊断信息，支持文本溢出提示
- **开单科室**：显示开单科室名称
- **开单医生**：显示开单医生姓名

### 3. 列宽调整
- **患者姓名**：100px → 120px
- **就诊号**：150px (新增)
- **处方号**：150px (保持)
- **处方时间**：180px (保持)
- **状态**：100px (新增)
- **诊断名称**：200px (新增，支持溢出提示)
- **开单科室**：120px (新增)
- **开单医生**：120px (新增)

### 4. 数据格式化
- **处方时间**：使用 `formatDate` 函数格式化为 `YYYY-MM-DD HH:mm:ss` 格式
- **状态**：使用 `el-tag` 组件显示，根据状态值显示不同颜色

## 新增辅助方法

### 处方状态文本转换
```typescript
const getPrescriptionStatusText = (value: number) => {
    switch (value) {
        case 0: return '未审核';
        case 1: return '未收费';
        case 2: return '已收费';
        case 3: return '已取药';
        case 4: return '已退药';
        case 5: return '已退费';
        case 6: return '作废';
        default: return '未知';
    }
};
```

### 处方状态样式类型
```typescript
const getPrescriptionStatusType = (value: number) => {
    switch (value) {
        case 0: return 'info';      // 未审核 - 灰色
        case 1: return 'warning';   // 未收费 - 橙色
        case 2: return 'primary';   // 已收费 - 蓝色
        case 3: return 'success';   // 已取药 - 绿色
        case 4:                     // 已退药 - 红色
        case 5:                     // 已退费 - 红色
        case 6: return 'danger';    // 作废 - 红色
        default: return '';
    }
};
```

## 状态说明

| 状态值 | 状态名称 | 标签颜色 | 说明 |
|--------|----------|----------|------|
| 0 | 未审核 | 灰色 (info) | 处方尚未审核 |
| 1 | 未收费 | 橙色 (warning) | 已审核但未收费 |
| 2 | 已收费 | 蓝色 (primary) | 已收费待取药 |
| 3 | 已取药 | 绿色 (success) | 已完成取药 |
| 4 | 已退药 | 红色 (danger) | 药品已退回 |
| 5 | 已退费 | 红色 (danger) | 费用已退回 |
| 6 | 作废 | 红色 (danger) | 处方已作废 |

## 用户体验优化

1. **信息完整性**：显示更多关键信息，便于医护人员快速了解处方详情
2. **状态可视化**：使用颜色标签直观显示处方状态
3. **文本溢出处理**：诊断名称支持鼠标悬停查看完整内容
4. **列宽优化**：合理分配列宽，确保信息显示完整

## 数据一致性

调整后的表格列完全对应后端 `SkinTestResultOutput` 类的属性，确保：
- 前后端数据字段一致
- 显示顺序与数据模型保持一致
- 数据类型和格式正确处理

## 总结

通过这次调整，处方主表的显示更加完整和规范，与后端数据模型保持高度一致，提升了用户体验和数据的可读性。同时，新增的状态显示和格式化功能使得医护人员能够更直观地了解处方的当前状态。
