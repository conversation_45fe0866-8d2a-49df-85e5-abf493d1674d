﻿# 皮试结果录入功能 API 使用说明

## 概述

皮试结果录入功能提供了完整的皮试结果管理能力，包括录入、批量录入、撤销和查询等功能。

## API 接口列表

### 1. 录入皮试结果

**接口地址：** `POST /api/SkinTestResult/Add`

**请求参数：**
```json
{
  "prescriptionDetailId": 123456,
  "skinTestResult": 1,
  "skinTestTime": "2024-01-01T10:00:00",
  "skinTestMethod": "皮内注射",
  "skinTestDose": "0.1ml",
  "reactionDescription": "注射部位无红肿，无硬结",
  "remark": "皮试阴性，可以使用"
}
```

**参数说明：**
- `prescriptionDetailId`: 处方详情ID（必填）
- `skinTestResult`: 皮试结果（必填）- 1:阴性, 2:阳性, 3:可疑
- `skinTestTime`: 皮试时间（可选，默认当前时间）
- `skinTestMethod`: 皮试方法（可选）
- `skinTestDose`: 皮试剂量（可选）
- `reactionDescription`: 反应描述（可选）
- `remark`: 备注（可选）

### 2. 批量录入皮试结果

**接口地址：** `POST /api/SkinTestResult/BatchAdd`

**请求参数：**
```json
{
  "prescriptionDetailIds": [123456, 123457, 123458],
  "skinTestResult": 1,
  "skinTestTime": "2024-01-01T10:00:00",
  "skinTestMethod": "皮内注射",
  "skinTestDose": "0.1ml",
  "reactionDescription": "注射部位无红肿，无硬结",
  "remark": "批量皮试阴性"
}
```

### 3. 撤销皮试结果

**接口地址：** `POST /api/SkinTestResult/Cancel`

**请求参数：**
```json
{
  "id": 789012,
  "cancelReason": "录入错误，需要重新皮试"
}
```

### 4. 分页查询皮试结果记录

**接口地址：** `POST /api/SkinTestResult/PageRecord`

**请求参数：**
```json
{
  "page": 1,
  "pageSize": 20,
  "patientName": "张三",
  "visitNo": "202401010001",
  "drugName": "青霉素",
  "skinTestResult": 1,
  "skinTestTimeRange": ["2024-01-01T00:00:00", "2024-01-31T23:59:59"],
  "status": 1
}
```

### 5. 查询需要皮试的处方

**接口地址：** `POST /api/SkinTestResult/Page`

**请求参数：**
```json
{
  "page": 1,
  "pageSize": 20,
  "prescriptionTimeRange": ["2024-01-01T00:00:00", "2024-01-31T23:59:59"],
  "billingDeptId": 12345,
  "isSkinTest": 1
}
```

### 6. 查询处方详情

**接口地址：** `POST /api/SkinTestResult/Detail`

**请求参数：**
```json
{
  "id": 123456
}
```

## 皮试结果枚举值

- `1`: 阴性 - 皮试无反应，可以使用该药品
- `2`: 阳性 - 皮试有反应，禁止使用该药品
- `3`: 可疑 - 皮试反应不明确，需要重新皮试或谨慎使用

## 状态枚举值

- `1`: 有效 - 皮试结果有效
- `2`: 撤销 - 皮试结果已撤销

## 业务规则

1. **录入前验证**：
   - 验证处方详情是否存在
   - 验证药品是否需要皮试（IsSkinTest = 1）
   - 验证是否已经录入过皮试结果

2. **权限控制**：
   - 只有有权限的医护人员才能录入皮试结果
   - 自动记录录入人员信息

3. **数据完整性**：
   - 皮试结果记录保存在专门的皮试结果表中
   - 同时更新处方详情表的皮试结果字段（保持兼容性）

4. **撤销规则**：
   - 只能撤销有效状态的皮试结果
   - 撤销时需要填写撤销原因
   - 撤销后会清空处方详情表的皮试结果字段

## 错误码说明

- `皮试结果值无效`: 传入的皮试结果值不在枚举范围内
- `未找到相关处方详情`: 处方详情ID不存在
- `药品不需要皮试`: 该药品的IsSkinTest字段不等于1
- `已录入皮试结果`: 该处方详情已经有有效的皮试结果记录
- `该皮试结果已被撤销`: 尝试撤销已经撤销的皮试结果

## 使用流程

1. **查询需要皮试的处方** → 获取需要皮试的处方列表
2. **查询处方详情** → 获取具体的药品信息
3. **录入皮试结果** → 单个或批量录入皮试结果
4. **查询皮试结果记录** → 查看历史皮试记录
5. **撤销皮试结果**（如需要） → 撤销错误的皮试结果
