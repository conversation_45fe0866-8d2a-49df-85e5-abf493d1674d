﻿namespace His.Module.OutpatientDoctor.Service.SkinTest.Dto;

public class SkinTestResultOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }

    /// <summary>
    /// 处方号
    /// </summary>
    public string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    public DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosticName { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>

    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生姓名
    /// </summary>
    public string? BillingDoctorName { get; set; }
}

public class SkinTestResultDetailOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 处方主表Id
    /// </summary>
    public long? PrescriptionId { get; set; }
    /// <summary>
    /// 药品Id
    /// </summary>
    public long? DrugId { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 单次量
    /// </summary>
    public decimal? SingleDose { get; set; }
    /// <summary>
    /// 单次量单位
    /// </summary>
    public string? SingleDoseUnit { get; set; }

    /// <summary>
    /// 给药途径Id
    /// </summary>
    public long? MedicationRoutesId { get; set; }

    /// <summary>
    /// 给药途径名称
    /// </summary>
    public string? MedicationRoutesName { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    public string? FrequencyName { get; set; }

    /// <summary>
    /// 皮试结果
    /// </summary>
    public int? SkinTestResults { get; set; }

    /// <summary>
    /// 皮试结果描述
    /// </summary>
    public string? SkinTestResultDesc { get; set; }

    /// <summary>
    /// 皮试时间
    /// </summary>
    public DateTime? SkinTestTime { get; set; }

    /// <summary>
    /// 皮试人员姓名
    /// </summary>
    public string? SkinTestStaffName { get; set; }
}

/// <summary>
/// 皮试结果记录输出
/// </summary>
public class SkinTestResultRecordOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 处方详情Id
    /// </summary>
    public long PrescriptionDetailId { get; set; }

    /// <summary>
    /// 处方主表Id
    /// </summary>
    public long PrescriptionId { get; set; }

    /// <summary>
    /// 药品Id
    /// </summary>
    public long DrugId { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }

    /// <summary>
    /// 皮试结果值
    /// </summary>
    public int SkinTestResultValue { get; set; }

    /// <summary>
    /// 皮试结果描述
    /// </summary>
    public string? SkinTestResultDesc { get; set; }

    /// <summary>
    /// 皮试时间
    /// </summary>
    public DateTime SkinTestTime { get; set; }

    /// <summary>
    /// 皮试人员姓名
    /// </summary>
    public string? SkinTestStaffName { get; set; }

    /// <summary>
    /// 皮试方法
    /// </summary>
    public string? SkinTestMethod { get; set; }

    /// <summary>
    /// 皮试剂量
    /// </summary>
    public string? SkinTestDose { get; set; }

    /// <summary>
    /// 反应描述
    /// </summary>
    public string? ReactionDescription { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string? StatusDesc { get; set; }

    /// <summary>
    /// 撤销时间
    /// </summary>
    public DateTime? CancelTime { get; set; }

    /// <summary>
    /// 撤销人员姓名
    /// </summary>
    public string? CancelStaffName { get; set; }

    /// <summary>
    /// 撤销原因
    /// </summary>
    public string? CancelReason { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
}