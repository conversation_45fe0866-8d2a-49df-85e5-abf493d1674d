﻿namespace His.Module.Inpatient;

/// <summary>
/// 住院预约输出参数
/// </summary>
public class AppointmentRecordOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }    
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }    
    
    /// <summary>
    /// 预约时间
    /// </summary>
    public DateTime? AppointmentTime { get; set; }    
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public int? IdCardType { get; set; }    
    
    /// <summary>
    /// 证件号码
    /// </summary>
    public string? IdCardNo { get; set; }    
    /// <summary>
    /// 就诊卡id
    /// </summary>
    public virtual long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string? MedicalCardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    public string? InsuranceNo { get; set; }    
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    public long? DeptId { get; set; }    
    
    /// <summary>
    /// 预约科室代码
    /// </summary>
    public string? DeptCode { get; set; }    
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public string? DeptName { get; set; }    
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    public long? DoctorId { get; set; }    
    
    /// <summary>
    /// 预约医生代码
    /// </summary>
    public string? DoctorCode { get; set; }    
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public string? DoctorName { get; set; }    
    
    /// <summary>
    /// 诊断代码
    /// </summary>
    public string? DiagnosticCode { get; set; }    
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosticName { get; set; }    
      /// <summary>
    /// 患者唯一号
    /// </summary>
    public virtual string? PatientNo { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>

    public string? Name { get; set; }

    /// <summary>
    /// 英文姓名
    /// </summary>

    public string? EnglishName { get; set; }

    /// <summary>
    /// 姓名拼音码
    /// </summary>

    [SugarColumn(ColumnDescription = "姓名拼音码", Length = 32)]
    public string? PinyinCode { get; set; }



    /// <summary>
    /// 性别
    /// </summary>

  
    public GenderEnum Sex { get; set; } = GenderEnum.Male;

    /// <summary>
    /// 年龄
    /// </summary>


    public int Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>


    public virtual string? AgeUnit { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>

    public DateTime? Birthday { get; set; }

 

 

    /// <summary>
    /// 民族
    /// </summary>
  
    public int? Nation { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>

    public string? Phone { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
 
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
  
    public string? ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>

    public string? ContactAddress { get; set; }

    /// <summary>
    /// 联系人电话号码
    /// </summary>

    public string? ContactPhone { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>

    public string? Nationality { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
   
    public string? Occupation { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>

    public int? Marriage { get; set; }
    // /// <summary>
    // /// 籍贯 
    // /// </summary>
   // public List<string>? NativePlace  { get; set; }
    /// <summary>
    /// 籍贯省
    /// </summary>
    
    public int? NativePlaceProvince { get; set; }
    
    /// <summary>
    /// 籍贯市
    /// </summary>
    
    public int? NativePlaceCity { get; set; }
    
    /// <summary>
    /// 籍贯县
    /// </summary>
    
    public int? NativePlaceCounty { get; set; }
    
    // /// <summary>
    // /// 出生地
    // /// </summary>
  //  public List<String> Birthplace { get; set; }
    
    /// <summary>
    /// 出生地省
    /// </summary>
    
    public int? BirthplaceProvince { get; set; }
    
    /// <summary>
    /// 出生地市
    /// </summary>
    
    public int? BirthplaceCity { get; set; }
    
    /// <summary>
    /// 出生地县
    /// </summary>
    
    public int? BirthplaceCounty { get; set; }
    // /// <summary>
    // /// 现居住地省
    // /// </summary>
   // public List<String> Residence { get; set; }
    /// <summary>
    /// 现居住地省
    /// </summary>
    
    
    public int ResidenceProvince { get; set; }
    
    /// <summary>
    /// 现居住地市
    /// </summary>
    
    public int ResidenceCity { get; set; }
    
    /// <summary>
    /// 现居住地县
    /// </summary>
    
    public int ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>
    
    public string? ResidenceAddress { get; set; }
    // /// <summary>
    // /// 工作地址
    // /// </summary>
    
    //public List<String> Work { get; set; }
    /// <summary>
    /// 工作地址省
    /// </summary>
    
    public int? WorkProvince { get; set; }
    
    /// <summary>
    /// 工作地址市
    /// </summary>
    
    public int? WorkCity { get; set; }
    
    /// <summary>
    /// 工作地址县
    /// </summary>
    
    public int? WorkCounty { get; set; }

    /// <summary>
    /// 详细工作地址
    /// </summary>

    public string? WorkAddress { get; set; }

    /// <summary>
    /// 工作单位
    /// </summary>

    public string? WorkPlace { get; set; }

    /// <summary>
    /// 单位电话
    /// </summary>

    public string? WorkPlacePhone { get; set; }

    /// <summary>
    /// 医保类别
    /// </summary>

    public int? MedInsCategory { get; set; }
    /// <summary>
    /// 费用类别
    /// </summary>
    public virtual long? FeeId { get; set; }
    // /// <summary>
    // /// 医保类型
    // /// </summary>
    // [SugarColumn(ColumnDescription = "医保类型")]
    // public MedInsTypeEnum? MedInsType { get; set; }
    /// <summary>
    /// 医保类型
    /// </summary>

    public int? MedInsType { get; set; }
    /// <summary>
    /// 医疗类别（费别）
    /// </summary>

    public long? MedCategory { get; set; }

        
    /// <summary>
    /// 入院途径
    /// </summary> 
    public virtual string? InpatientWay { get; set; }
    /// <summary>
    /// 险种类型
    /// </summary>

    public string? InsuranceType { get; set; }

    /// <summary>
    /// 是否无卡
    /// </summary>

    public YesNoEnum IsNoCard { get; set; }

    /// <summary>
    /// 医保卡信息
    /// </summary>
    public string? MedInsCardInfo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 创建组织ID
    /// </summary>
    public long? CreateOrgId { get; set; }    
    
    /// <summary>
    /// 创建组织名称
    /// </summary>
    public string? CreateOrgName { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 住院预约数据导入模板实体
/// </summary>
public class ExportAppointmentRecordOutput : ImportAppointmentRecordInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
