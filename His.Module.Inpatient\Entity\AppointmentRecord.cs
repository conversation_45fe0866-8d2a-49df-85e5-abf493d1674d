﻿using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 住院预约表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("appointment_record", "住院预约表")]
public class AppointmentRecord : EntityTenant
{
    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 100)]
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 100)]
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 预约时间
    /// </summary>
    [SugarColumn(ColumnName = "appointment_time", ColumnDescription = "预约时间")]
    public virtual DateTime? AppointmentTime { get; set; }
    /// <summary>
    /// 就诊卡id
    /// </summary>
    [SugarColumn(ColumnName = "medical_card_id", ColumnDescription = "就诊卡id")]
    public virtual long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnName = "medical_card_no", ColumnDescription = "就诊卡号", Length = 100)]
    public virtual string? MedicalCardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 100)]
    public virtual string? OutpatientNo { get; set; }
 
    /// <summary>
    /// 证件类型
    /// </summary>
    [SugarColumn(ColumnName = "id_card_type", ColumnDescription = "证件类型", Length = 100)]
    public virtual string? IdCardType { get; set; }
    /// <summary>
    /// 费用类别
    /// </summary>
    [SugarColumn(ColumnName = "fee_id", ColumnDescription = "费用类别", Length = 100)]
    public virtual long? FeeId { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_way", ColumnDescription = "入院途径", Length = 100)]
    public virtual string? InpatientWay { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    [SugarColumn(ColumnName = "id_card_no", ColumnDescription = "证件号码", Length = 100)]
    public virtual string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    [SugarColumn(ColumnName = "insurance_no", ColumnDescription = "保险号", Length = 100)]
    public virtual string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "预约科室ID")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室代码
    /// </summary>
    [SugarColumn(ColumnName = "dept_code", ColumnDescription = "预约科室代码", Length = 100)]
    public virtual string? DeptCode { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "预约科室名称", Length = 100)]
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "预约医生ID")]
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生代码
    /// </summary>
    [SugarColumn(ColumnName = "doctor_code", ColumnDescription = "预约医生代码", Length = 100)]
    public virtual string? DoctorCode { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "doctor_name", ColumnDescription = "预约医生姓名", Length = 100)]
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 诊断代码
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic_code", ColumnDescription = "诊断代码", Length = 100)]
    public virtual string? DiagnosticCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic_name", ColumnDescription = "诊断名称", Length = 100)]
    public virtual string? DiagnosticName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 500)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 创建组织ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建组织ID")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建组织名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建组织名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
