﻿namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 皮试结果表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("skin_test_result", "皮试结果表")]
public class SkinTestResult : EntityTenantBaseData
{
    /// <summary>
    /// 处方详情Id
    /// </summary>

    [SugarColumn(ColumnName = "prescription_detail_id", ColumnDescription = "处方详情Id")]
    public virtual long PrescriptionDetailId { get; set; }

    /// <summary>
    /// 处方主表Id
    /// </summary>

    [SugarColumn(ColumnName = "prescription_id", ColumnDescription = "处方主表Id")]
    public virtual long? PrescriptionId { get; set; }

    /// <summary>
    /// 药品Id
    /// </summary>

    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品Id")]
    public virtual long? DrugId { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 200)]
    public virtual string? DrugName { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 64)]
    public virtual string? PatientName { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 64)]
    public virtual string? VisitNo { get; set; }

    /// <summary>
    /// 皮试结果
    /// </summary>

    [SugarColumn(ColumnName = "skin_test_result", ColumnDescription = "皮试结果 1阴性 2阳性 3可疑")]
    public virtual string SkinTestResultValue { get; set; }

    /// <summary>
    /// 皮试时间
    /// </summary>

    [SugarColumn(ColumnName = "skin_test_time", ColumnDescription = "皮试时间")]
    public virtual DateTime SkinTestTime { get; set; }

    /// <summary>
    /// 皮试人员Id
    /// </summary>
    [SugarColumn(ColumnName = "skin_test_staff_id", ColumnDescription = "皮试人员Id")]
    public virtual long? SkinTestStaffId { get; set; }

    /// <summary>
    /// 皮试人员姓名
    /// </summary>
    [SugarColumn(ColumnName = "skin_test_staff_name", ColumnDescription = "皮试人员姓名", Length = 64)]
    public virtual string? SkinTestStaffName { get; set; }

    /// <summary>
    /// 皮试方法
    /// </summary>
    [SugarColumn(ColumnName = "skin_test_method", ColumnDescription = "皮试方法", Length = 100)]
    public virtual string? SkinTestMethod { get; set; }

    /// <summary>
    /// 皮试剂量
    /// </summary>
    [SugarColumn(ColumnName = "skin_test_dose", ColumnDescription = "皮试剂量", Length = 50)]
    public virtual string? SkinTestDose { get; set; }

    /// <summary>
    /// 反应描述
    /// </summary>
    [SugarColumn(ColumnName = "reaction_description", ColumnDescription = "反应描述", ColumnDataType = "text")]
    public virtual string? ReactionDescription { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 500)]
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 状态 1有效 2撤销
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 1有效 2撤销")]
    public virtual int Status { get; set; } = 1;

    /// <summary>
    /// 撤销时间
    /// </summary>
    [SugarColumn(ColumnName = "cancel_time", ColumnDescription = "撤销时间")]
    public virtual DateTime? CancelTime { get; set; }

    /// <summary>
    /// 撤销人员Id
    /// </summary>
    [SugarColumn(ColumnName = "cancel_staff_id", ColumnDescription = "撤销人员Id")]
    public virtual long? CancelStaffId { get; set; }

    /// <summary>
    /// 撤销人员姓名
    /// </summary>
    [SugarColumn(ColumnName = "cancel_staff_name", ColumnDescription = "撤销人员姓名", Length = 64)]
    public virtual string? CancelStaffName { get; set; }

    /// <summary>
    /// 撤销原因
    /// </summary>
    [SugarColumn(ColumnName = "cancel_reason", ColumnDescription = "撤销原因", Length = 200)]
    public virtual string? CancelReason { get; set; }
}
