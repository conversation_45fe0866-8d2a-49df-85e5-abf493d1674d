﻿using Admin.NET.Core.Service;
using His.Module.Inpatient.OtherModuleEntity;
using Microsoft.AspNetCore.Http;

namespace His.Module.Inpatient.Service;

/// <summary>
/// 住院登记服务 🧩
/// </summary>
[ApiDescriptionSettings(InpatientConst.GroupName, Order = 100)]
public class InpatientRegisterService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<InpatientRegister> _inpatientRegisterRep;
    private readonly SqlSugarRepository<AppointmentRecord> _appointmentRecordRep;
    private readonly SqlSugarRepository<PatientInfo> _patientInfoRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public InpatientRegisterService(SqlSugarRepository<InpatientRegister> inpatientRegisterRep,
        SqlSugarRepository<PatientInfo> patientInfoRep,
        SqlSugarRepository<AppointmentRecord> appointmentRecordRep,
        ISqlSugarClient sqlSugarClient)
    {
        _inpatientRegisterRep = inpatientRegisterRep;
        _sqlSugarClient = sqlSugarClient;
        _patientInfoRep= patientInfoRep;
        _appointmentRecordRep= appointmentRecordRep;
        
    }
    [DisplayName("获取前*条诊断")]
    [ApiDescriptionSettings(Name = "GetDiagnosticMap"), HttpGet]
    public async Task<List<Dictionary<string,string>>> GetDiagnosticMap([FromQuery][Required] string key,[FromQuery] int top=20)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            return [];
        }
        var tables=await  _inpatientRegisterRep.Context.Ado.GetDataTableAsync($"""
       select code,name from shared.icd10
                   where status=1 and is_delete=false and
                         (pinyin_code like '%{key}%' or name like '%{key}%') limit {top}
       """ );
          return tables.Select().ToList().Select(x => new Dictionary<string, string>
          {
              { "code",x["code"].ToString()},
              { "name",x["name"].ToString()}
          }).ToList();
    }

    
    /// <summary>
    /// 分页查询住院登记 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询住院登记")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<InpatientRegisterOutput>> Page(PageInpatientRegisterInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _inpatientRegisterRep
                .AsTenant().QueryableWithAttr<InpatientRegister>()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.PatientNo.Contains(input.Keyword) || u.PatientName.Contains(input.Keyword)  || u.IdCardNo.Contains(input.Keyword) || u.InsuranceNo.Contains(input.Keyword) || u.MedicalCardNo.Contains(input.Keyword) || u.OutpatientNo.Contains(input.Keyword) || u.InpatientNo.Contains(input.Keyword) || u.InpatientSerialNo.Contains(input.Keyword) || u.MedicalRecordNo.Contains(input.Keyword) || u.DoctorName.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.MainDoctorName.Contains(input.Keyword) || u.InpatientDiagnosticCode.Contains(input.Keyword) || u.InpatientDiagnosticName.Contains(input.Keyword) || u.SecondaryDiagnosticCode.Contains(input.Keyword) || u.SecondaryDiagnosticName.Contains(input.Keyword) || u.InpatientWay.Contains(input.Keyword) || u.GuaranteePerson.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientNo), u => u.PatientNo.Contains(input.PatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))

            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardNo), u => u.IdCardNo.Contains(input.IdCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InsuranceNo), u => u.InsuranceNo.Contains(input.InsuranceNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicalCardNo), u => u.MedicalCardNo.Contains(input.MedicalCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo), u => u.OutpatientNo.Contains(input.OutpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientNo), u => u.InpatientNo.Contains(input.InpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientSerialNo), u => u.InpatientSerialNo.Contains(input.InpatientSerialNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicalRecordNo), u => u.MedicalRecordNo.Contains(input.MedicalRecordNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DoctorName), u => u.DoctorName.Contains(input.DoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeptName), u => u.DeptName.Contains(input.DeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MainDoctorName), u => u.MainDoctorName.Contains(input.MainDoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientDiagnosticCode), u => u.InpatientDiagnosticCode.Contains(input.InpatientDiagnosticCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientDiagnosticName), u => u.InpatientDiagnosticName.Contains(input.InpatientDiagnosticName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SecondaryDiagnosticCode), u => u.SecondaryDiagnosticCode.Contains(input.SecondaryDiagnosticCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SecondaryDiagnosticName), u => u.SecondaryDiagnosticName.Contains(input.SecondaryDiagnosticName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientWay), u => u.InpatientWay.Contains(input.InpatientWay.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.GuaranteePerson), u => u.GuaranteePerson.Contains(input.GuaranteePerson.Trim()))
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.IdCardType.HasValue, u => u.IdCardType == (int)input.IdCardType)
            .WhereIF(input.MedicalCardId != null, u => u.MedicalCardId == input.MedicalCardId)
            .WhereIF(input.InpatientTimes != null, u => u.InpatientTimes == input.InpatientTimes)
            .WhereIF(input.DoctorId != null, u => u.DoctorId == input.DoctorId)
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.MainDoctorId != null, u => u.MainDoctorId == input.MainDoctorId)
            .WhereIF(input.InpatientTimeRange?.Length == 2, u => u.InpatientTime >= input.InpatientTimeRange[0] && u.InpatientTime <= input.InpatientTimeRange[1])
            .WhereIF(input.NewbornBirthWeight1 != null, u => u.NewbornBirthWeight1 == input.NewbornBirthWeight1)
            .WhereIF(input.NewbornBirthWeight2 != null, u => u.NewbornBirthWeight2 == input.NewbornBirthWeight2)
            .WhereIF(input.NewbornBirthWeight3 != null, u => u.NewbornBirthWeight3 == input.NewbornBirthWeight3)
            .WhereIF(input.NewbornInpatientWeight != null, u => u.NewbornInpatientWeight == input.NewbornInpatientWeight)
            .WhereIF(input.HasMedicalInsurance != null, u => u.HasMedicalInsurance == input.HasMedicalInsurance)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<PatientInfo>((u, patient) => u.PatientId == patient.Id)
            .Select((u, patient) => new InpatientRegisterOutput
            {
                Id = u.Id,
                PatientId = u.PatientId,
                PatientFkDisplayName = $"{patient.Name}",
                PatientNo = u.PatientNo,
                PatientName = u.PatientName,
                AppointmentRecordId=u.AppointmentRecordId,
                IdCardType =(CardTypeEnum)u.IdCardType,
                IdCardNo = u.IdCardNo,
                InsuranceNo = u.InsuranceNo,
                MedicalCardId = u.MedicalCardId,
                MedicalCardNo = u.MedicalCardNo,
                OutpatientNo = u.OutpatientNo,
                InpatientNo = u.InpatientNo,
                InpatientSerialNo = u.InpatientSerialNo,
                InpatientTimes = u.InpatientTimes,
                MedicalRecordNo = u.MedicalRecordNo,
                FeeName = u.FeeName,
                FeeId = u.FeeId,
                DoctorId = u.DoctorId,
                DoctorName = u.DoctorName,
                DeptId = u.DeptId,
                DeptName = u.DeptName,
                MainDoctorId = u.MainDoctorId,
                MainDoctorName = u.MainDoctorName,
                InpatientDiagnosticCode = u.InpatientDiagnosticCode,
                InpatientDiagnosticName = u.InpatientDiagnosticName,
                SecondaryDiagnosticCode = u.SecondaryDiagnosticCode,
                SecondaryDiagnosticName = u.SecondaryDiagnosticName,
                InpatientWay = u.InpatientWay,
                InpatientTime = u.InpatientTime,
                AllowArrears = u.AllowArrears,
                ArrearsLimit = u.ArrearsLimit,
                GuaranteePerson = u.GuaranteePerson,
                NewbornBirthWeight1 = u.NewbornBirthWeight1,
                NewbornBirthWeight2 = u.NewbornBirthWeight2,
                NewbornBirthWeight3 = u.NewbornBirthWeight3,
                NewbornInpatientWeight = u.NewbornInpatientWeight,
                HasMedicalInsurance = u.HasMedicalInsurance,
                ContactName = u.ContactName,
                ContactPhone = u.ContactPhone,
          ContactAddress = u.ContactAddress,
          ContactRelationship = u.ContactRelationship,
          ContactAddressStreet = u.ContactAddressStreet,
                Status = u.Status,
                CreateOrgId = u.CreateOrgId,
                CreateOrgName = u.CreateOrgName,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取住院登记详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取住院登记详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<InpatientRegister> Detail([FromQuery] QueryByIdInpatientRegisterInput input)
    {
        return await _inpatientRegisterRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加住院登记 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加住院登记")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<long> Add(AddInpatientRegisterInput input)
    {
        // 生成住院号
        // 住院次数
        // 住院流水号
        //病案号
        // entity.StorageInNo = await _storageInRecordRep.Context.Ado.GetStringAsync(
        //     "SELECT LPAD(CAST(NEXTVAL('storage_in_record_no_seq')As varchar),7,'0')");

        var registerInfo = input.RegisterInfo;
        // 验证预约记录
        if (registerInfo.AppointmentRecordId is > 0)
        {
            var exist =
                await _inpatientRegisterRep.AsQueryable().Where(u => u.AppointmentRecordId == registerInfo.AppointmentRecordId)
                    .CountAsync();
            if (exist > 0)
            {
                throw new Exception("该预约记录已登记");
            }	
            //修改状态
           await _appointmentRecordRep.UpdateAsync(it => new AppointmentRecord{ Status = 1 }
           ,it => it.Id == registerInfo.AppointmentRecordId) ;
           
        } 

        
        if (String.IsNullOrWhiteSpace(registerInfo.InpatientNo))
        {
            // 生成住院号
            var inpatientNo = await _inpatientRegisterRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('register_inpatient_no_seq')As varchar),5,'0')"
            );
            registerInfo.InpatientNo =DateTime.Now.ToString("yyyyMM")+inpatientNo;
            registerInfo.InpatientSerialNo =inpatientNo;
            //病案号
            registerInfo.MedicalRecordNo = await _inpatientRegisterRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('register_medical_record_no_seq')As varchar),8,'0')"
            );
            registerInfo.InpatientTimes = 1;
          
        }
        else
        {
            // 生成住院流水号
            var serialNo = await _inpatientRegisterRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('register_inpatient_serial_no_seq')As varchar),5,'0')"
            );
            registerInfo.InpatientSerialNo =DateTime.Now.ToString("yyyyMM")+serialNo;
            // 住院次数
            registerInfo.InpatientTimes  = await _inpatientRegisterRep.AsQueryable().Where
                    (u => u.InpatientNo == registerInfo.InpatientNo)
                    .CountAsync();
            
        }
        registerInfo.InpatientTime = DateTime.Now; 
        registerInfo.InpatientTime = DateTime.Now; 
        var entity = registerInfo.Adapt<InpatientRegister>();  
        return await _inpatientRegisterRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新住院登记 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新住院登记")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateInpatientRegisterInput input)
    {
        var entity = input.Adapt<InpatientRegister>();
        await _inpatientRegisterRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除住院登记 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除住院登记")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteInpatientRegisterInput input)
    {
        var entity = await _inpatientRegisterRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _inpatientRegisterRep.FakeDeleteAsync(entity);   //假删除
        //await _inpatientRegisterRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除住院登记 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除住院登记")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteInpatientRegisterInput> input)
    {
        var exp = Expressionable.Create<InpatientRegister>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _inpatientRegisterRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _inpatientRegisterRep.FakeDeleteAsync(list);   //假删除
        //return await _inpatientRegisterRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataInpatientRegisterInput input)
    {
        // var patientIdData = await _patientInfoRep.Context.Queryable<PatientInfo>()
        //     .InnerJoinIF<InpatientRegister>(input.FromPage, (u, r) => u.Id == r.PatientId)
        //     .Select(u => new {
        //         Value = u.Id,
        //         Label = $"{u.Name}-{u.Sex}-{u.Birthday}-{u.AgeUnit}"
        //     }).ToListAsync();
       var depts= await _inpatientRegisterRep.Context.Ado.GetDataTableAsync("select id,code,name from basic.sys_org  where is_delete=false and pid=(select id from basic.sys_org where code='********' and  is_delete=false limit 1)");
       var doctors= await _inpatientRegisterRep.Context.Ado.GetDataTableAsync("select id,account as code,real_name as name from basic.sys_user  where is_delete=false");

        return new Dictionary<string, dynamic>
        {
            { "patientId", null },
            { "depts", depts.Select().Select(x => new { Value = x["id"], Label =  x["name"] }) },
            { "doctors", doctors.Select().Select(x => new { Value = x["id"], Label =  x["name"] }) } 
        };
    }
    
    /// <summary>
    /// 导出住院登记记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出住院登记记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageInpatientRegisterInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportInpatientRegisterOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "住院登记导出记录");
    }
    
    /// <summary>
    /// 下载住院登记数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载住院登记数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportInpatientRegisterOutput>(), "住院登记导入模板", (_, info) =>
        {
            if (nameof(ExportInpatientRegisterOutput.PatientFkDisplayName) == info.Name) return _inpatientRegisterRep.Context.Queryable<PatientInfo>().Select(u => $"{u.Name}-{u.Sex}-{u.Birthday}-{u.AgeUnit}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入住院登记记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入住院登记记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportInpatientRegisterInput, InpatientRegister>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 患者ID
                    var patientIdLabelList = pageItems.Where(x => x.PatientFkDisplayName != null).Select(x => x.PatientFkDisplayName).Distinct().ToList();
                    if (patientIdLabelList.Any()) {
                        var patientIdLinkMap = _inpatientRegisterRep.Context.Queryable<PatientInfo>().Where(u => patientIdLabelList.Contains($"{u.Name}-{u.Sex}-{u.Birthday}-{u.AgeUnit}")).ToList().ToDictionary(u => $"{u.Name}-{u.Sex}-{u.Birthday}-{u.AgeUnit}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.PatientId = patientIdLinkMap.GetValueOrDefault(e.PatientFkDisplayName ?? "");
                            if (e.PatientId == null) e.Error = "患者ID链接失败";
                        });
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<InpatientRegister>>();
                    
                    var storageable = _inpatientRegisterRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.PatientNo?.Length > 100, "患者编号长度不能超过100个字符")
                        .SplitError(it => it.Item.PatientName?.Length > 100, "患者姓名长度不能超过100个字符")
                        .SplitError(it => it.Item.IdCardNo?.Length > 100, "证件号码长度不能超过100个字符")
                        .SplitError(it => it.Item.InsuranceNo?.Length > 100, "保险号长度不能超过100个字符")
                        .SplitError(it => it.Item.MedicalCardNo?.Length > 100, "就诊卡号长度不能超过100个字符")
                        .SplitError(it => it.Item.OutpatientNo?.Length > 100, "门诊号长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientNo?.Length > 100, "住院号长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientSerialNo?.Length > 100, "住院流水号长度不能超过100个字符")
                        .SplitError(it => it.Item.MedicalRecordNo?.Length > 100, "病案号长度不能超过100个字符")
                        .SplitError(it => it.Item.DoctorName?.Length > 100, "医生姓名长度不能超过100个字符")
                        .SplitError(it => it.Item.DeptName?.Length > 100, "科室名称长度不能超过100个字符")
                        .SplitError(it => it.Item.MainDoctorName?.Length > 100, "主治医生姓名长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientDiagnosticCode?.Length > 100, "入院诊断代码长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientDiagnosticName?.Length > 100, "入院诊断名称长度不能超过100个字符")
                        .SplitError(it => it.Item.SecondaryDiagnosticCode?.Length > 100, "次要诊断代码长度不能超过100个字符")
                        .SplitError(it => it.Item.SecondaryDiagnosticName?.Length > 100, "次要诊断名称长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientWay?.Length > 100, "入院途径长度不能超过100个字符")
                        .SplitError(it => it.Item.GuaranteePerson?.Length > 100, "担保人长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
