﻿-- 创建皮试结果表
CREATE TABLE `skin_test_result` (
  `Id` bigint NOT NULL COMMENT '主键Id',
  `TenantId` bigint DEFAULT NULL COMMENT '租户Id',
  `prescription_detail_id` bigint NOT NULL COMMENT '处方详情Id',
  `prescription_id` bigint NOT NULL COMMENT '处方主表Id',
  `drug_id` bigint NOT NULL COMMENT '药品Id',
  `drug_code` varchar(100) DEFAULT NULL COMMENT '药品编码',
  `drug_name` varchar(200) DEFAULT NULL COMMENT '药品名称',
  `patient_id` bigint NOT NULL COMMENT '患者Id',
  `patient_name` varchar(64) DEFAULT NULL COMMENT '患者姓名',
  `visit_no` varchar(64) DEFAULT NULL COMMENT '就诊号',
  `skin_test_result` int NOT NULL COMMENT '皮试结果 1阴性 2阳性 3可疑',
  `skin_test_time` datetime NOT NULL COMMENT '皮试时间',
  `skin_test_staff_id` bigint DEFAULT NULL COMMENT '皮试人员Id',
  `skin_test_staff_name` varchar(64) DEFAULT NULL COMMENT '皮试人员姓名',
  `skin_test_method` varchar(100) DEFAULT NULL COMMENT '皮试方法',
  `skin_test_dose` varchar(50) DEFAULT NULL COMMENT '皮试剂量',
  `reaction_description` text COMMENT '反应描述',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int DEFAULT '1' COMMENT '状态 1有效 2撤销',
  `cancel_time` datetime DEFAULT NULL COMMENT '撤销时间',
  `cancel_staff_id` bigint DEFAULT NULL COMMENT '撤销人员Id',
  `cancel_staff_name` varchar(64) DEFAULT NULL COMMENT '撤销人员姓名',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '撤销原因',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  `UpdateTime` datetime DEFAULT NULL COMMENT '更新时间',
  `CreateUserId` bigint DEFAULT NULL COMMENT '创建者Id',
  `CreateUserName` varchar(64) DEFAULT NULL COMMENT '创建者姓名',
  `UpdateUserId` bigint DEFAULT NULL COMMENT '修改者Id',
  `UpdateUserName` varchar(64) DEFAULT NULL COMMENT '修改者姓名',
  `IsDelete` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`Id`),
  KEY `idx_prescription_detail_id` (`prescription_detail_id`),
  KEY `idx_prescription_id` (`prescription_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_visit_no` (`visit_no`),
  KEY `idx_skin_test_time` (`skin_test_time`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`TenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='皮试结果表';

-- 添加外键约束（可选）
-- ALTER TABLE `skin_test_result` ADD CONSTRAINT `fk_skin_test_result_prescription_detail` 
-- FOREIGN KEY (`prescription_detail_id`) REFERENCES `prescription_detail` (`Id`) ON DELETE CASCADE;

-- ALTER TABLE `skin_test_result` ADD CONSTRAINT `fk_skin_test_result_prescription_main` 
-- FOREIGN KEY (`prescription_id`) REFERENCES `prescription_main` (`Id`) ON DELETE CASCADE;
