﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Inpatient;

/// <summary>
/// 住院登记基础输入参数
/// </summary>
public class InpatientRegisterBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者编号
    /// </summary>
    public virtual string? PatientNo { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string? PatientName { get; set; }
    
 
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [Dict(nameof(CardTypeEnum), AllowNullValue=true)]
    public virtual CardTypeEnum? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    public virtual string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    public virtual string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 就诊卡ID
    /// </summary>
    public virtual long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    public virtual string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    public virtual string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院次数
    /// </summary>
    public virtual int? InpatientTimes { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    public virtual string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 费别Id
    /// </summary>
    public    long? FeeId { get; set; }
    /// <summary>
    /// 费别
    /// </summary>
  
    public    string? FeeName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 主治医生ID
    /// </summary>
    public virtual long? MainDoctorId { get; set; }
    
    /// <summary>
    /// 主治医生姓名
    /// </summary>
    public virtual string? MainDoctorName { get; set; }
    
    /// <summary>
    /// 入院诊断代码
    /// </summary>
    public virtual string? InpatientDiagnosticCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public virtual string? InpatientDiagnosticName { get; set; }
    
    /// <summary>
    /// 次要诊断代码
    /// </summary>
    public virtual string? SecondaryDiagnosticCode { get; set; }
    
    /// <summary>
    /// 次要诊断名称
    /// </summary>
    public virtual string? SecondaryDiagnosticName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    public virtual string? InpatientWay { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>
    public virtual DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 是否允许欠费
    /// </summary>
    public virtual bool? AllowArrears { get; set; }
    
    /// <summary>
    /// 欠费上限
    /// </summary>
    public virtual decimal? ArrearsLimit { get; set; }
    
    /// <summary>
    /// 担保人
    /// </summary>
    public virtual string? GuaranteePerson { get; set; }
    
    /// <summary>
    /// 新生儿出生体重g
    /// </summary>
    public virtual int? NewbornBirthWeight1 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    public virtual int? NewbornBirthWeight2 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    public virtual int? NewbornBirthWeight3 { get; set; }
    
    /// <summary>
    /// 新生儿入院体重g
    /// </summary>
    public virtual int? NewbornInpatientWeight { get; set; }
    
    /// <summary>
    /// 是否有医保卡
    /// </summary>
    public virtual int? HasMedicalInsurance { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 住院登记分页查询输入参数
/// </summary>
public class  PageInpatientRegisterInput : BasePageInput
{
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者编号
    /// </summary>
    public string? PatientNo { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    
 
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [Dict(nameof(CardTypeEnum), AllowNullValue=true)]
    public CardTypeEnum? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 就诊卡ID
    /// </summary>
    public long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院次数
    /// </summary>
    public int? InpatientTimes { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    public string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 费别Id
    /// </summary>
    public    long? FeeId { get; set; }
    /// <summary>
    /// 费别
    /// </summary>
  
    public    string? FeeName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 主治医生ID
    /// </summary>
    public long? MainDoctorId { get; set; }
    
    /// <summary>
    /// 主治医生姓名
    /// </summary>
    public string? MainDoctorName { get; set; }
    
    /// <summary>
    /// 入院诊断代码
    /// </summary>
    public string? InpatientDiagnosticCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public string? InpatientDiagnosticName { get; set; }
    
    /// <summary>
    /// 次要诊断代码
    /// </summary>
    public string? SecondaryDiagnosticCode { get; set; }
    
    /// <summary>
    /// 次要诊断名称
    /// </summary>
    public string? SecondaryDiagnosticName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 入院时间范围
    /// </summary>
     public DateTime?[] InpatientTimeRange { get; set; }
    
    /// <summary>
    /// 是否允许欠费
    /// </summary>
    public bool? AllowArrears { get; set; }
    
    /// <summary>
    /// 欠费上限
    /// </summary>
    public decimal? ArrearsLimit { get; set; }
    
    /// <summary>
    /// 担保人
    /// </summary>
    public string? GuaranteePerson { get; set; }
    
    /// <summary>
    /// 新生儿出生体重g
    /// </summary>
    public int? NewbornBirthWeight1 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    public int? NewbornBirthWeight2 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    public int? NewbornBirthWeight3 { get; set; }
    
    /// <summary>
    /// 新生儿入院体重g
    /// </summary>
    public int? NewbornInpatientWeight { get; set; }
    
    /// <summary>
    /// 是否有医保卡
    /// </summary>
    public bool? HasMedicalInsurance { get; set; }
    /// <summary>
    /// 联系人
    /// </summary>
 
    public   string ContactName { get; set; }

    // 联系电话
 
    public   string ContactPhone { get; set; }
    
    /// <summary>
    /// 联系人关系
    /// </summary>
 
    public   string ContactRelationship { get; set; }
    
    public List<String>    ContactAddress { get; set; }
 

    // 联系地址
    public   string ContactAddressStreet { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 住院登记增加输入参数
/// </summary>
public class RegisterInfo
{    /// <summary>
    /// 预约记录id
    /// </summary>
    public long? AppointmentRecordId { get; set; }
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者编号
    /// </summary>
    [MaxLength(100, ErrorMessage = "患者编号字符长度不能超过100")]
    public string? PatientNo { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "患者姓名字符长度不能超过100")]
    public string? PatientName { get; set; }
    
 
    /// <summary>
    /// 证件类型
    /// </summary>
    [Dict(nameof(CardTypeEnum), AllowNullValue=true)]
    public CardTypeEnum? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    [MaxLength(100, ErrorMessage = "证件号码字符长度不能超过100")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    [MaxLength(100, ErrorMessage = "保险号字符长度不能超过100")]
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 就诊卡ID
    /// </summary>
    public long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [MaxLength(100, ErrorMessage = "就诊卡号字符长度不能超过100")]
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [MaxLength(100, ErrorMessage = "门诊号字符长度不能超过100")]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [MaxLength(100, ErrorMessage = "住院号字符长度不能超过100")]
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [MaxLength(100, ErrorMessage = "住院流水号字符长度不能超过100")]
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院次数
    /// </summary>
    public int? InpatientTimes { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    [MaxLength(100, ErrorMessage = "病案号字符长度不能超过100")]
    public string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 费别Id
    /// </summary>
    public    long? FeeId { get; set; }
    /// <summary>
    /// 费别
    /// </summary>
  
    public    string? FeeName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "医生姓名字符长度不能超过100")]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "科室名称字符长度不能超过100")]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 主治医生ID
    /// </summary>
    public long? MainDoctorId { get; set; }
    
    /// <summary>
    /// 主治医生姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "主治医生姓名字符长度不能超过100")]
    public string? MainDoctorName { get; set; }
    
    /// <summary>
    /// 入院诊断代码
    /// </summary>
    [MaxLength(100, ErrorMessage = "入院诊断代码字符长度不能超过100")]
    public string? InpatientDiagnosticCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "入院诊断名称字符长度不能超过100")]
    public string? InpatientDiagnosticName { get; set; }
    
    /// <summary>
    /// 次要诊断代码
    /// </summary>
    [MaxLength(100, ErrorMessage = "次要诊断代码字符长度不能超过100")]
    public string? SecondaryDiagnosticCode { get; set; }
    
    /// <summary>
    /// 次要诊断名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "次要诊断名称字符长度不能超过100")]
    public string? SecondaryDiagnosticName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    [MaxLength(100, ErrorMessage = "入院途径字符长度不能超过100")]
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>
    public DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 是否允许欠费
    /// </summary>
    public bool? AllowArrears { get; set; }
    
    /// <summary>
    /// 欠费上限
    /// </summary>
    public decimal? ArrearsLimit { get; set; }
    
    /// <summary>
    /// 担保人
    /// </summary>
    [MaxLength(100, ErrorMessage = "担保人字符长度不能超过100")]
    public string? GuaranteePerson { get; set; }
    
    /// <summary>
    /// 新生儿出生体重g
    /// </summary>
    public int? NewbornBirthWeight1 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    public int? NewbornBirthWeight2 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    public int? NewbornBirthWeight3 { get; set; }
    
    /// <summary>
    /// 新生儿入院体重g
    /// </summary>
    public int? NewbornInpatientWeight { get; set; }
    
    /// <summary>
    /// 是否有医保卡
    /// </summary>
    public int? HasMedicalInsurance { get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>

    public string ContactName { get; set; }

    // 联系电话

    public string ContactPhone { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>

    public string ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地区
    /// </summary>
    public List<String> ContactAddress { get; set; }


    /// <summary>
    /// 联系地址
    /// </summary>
    public string ContactAddressStreet { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
}
public class AddInpatientRegisterInput
{   
    // 登记信息
    public RegisterInfo RegisterInfo { get; set; }
    // 医保信息
    
    /// <summary>
    /// 预交金信息
    /// </summary>
    public AdvancePaymentDto AdvancePaymentInfo { get; set; } 
}

/// <summary>
/// 住院登记删除输入参数
/// </summary>
public class DeleteInpatientRegisterInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 住院登记更新输入参数
/// </summary>
public class UpdateInpatientRegisterInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>    
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者编号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "患者编号字符长度不能超过100")]
    public string? PatientNo { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>    
    [MaxLength(100, ErrorMessage = "患者姓名字符长度不能超过100")]
    public string? PatientName { get; set; }
 
    
    /// <summary>
    /// 证件类型
    /// </summary>    
    [Dict(nameof(CardTypeEnum), AllowNullValue=true)]
    public CardTypeEnum? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "证件号码字符长度不能超过100")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "保险号字符长度不能超过100")]
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 就诊卡ID
    /// </summary>    
    public long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "就诊卡号字符长度不能超过100")]
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "门诊号字符长度不能超过100")]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "住院号字符长度不能超过100")]
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "住院流水号字符长度不能超过100")]
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院次数
    /// </summary>    
    public int? InpatientTimes { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "病案号字符长度不能超过100")]
    public string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 费别Id
    /// </summary>
    public    long? FeeId { get; set; }
    /// <summary>
    /// 费别
    /// </summary>
  
    public    string? FeeName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>    
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>    
    [MaxLength(100, ErrorMessage = "医生姓名字符长度不能超过100")]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>    
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "科室名称字符长度不能超过100")]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 主治医生ID
    /// </summary>    
    public long? MainDoctorId { get; set; }
    
    /// <summary>
    /// 主治医生姓名
    /// </summary>    
    [MaxLength(100, ErrorMessage = "主治医生姓名字符长度不能超过100")]
    public string? MainDoctorName { get; set; }
    
    /// <summary>
    /// 入院诊断代码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "入院诊断代码字符长度不能超过100")]
    public string? InpatientDiagnosticCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "入院诊断名称字符长度不能超过100")]
    public string? InpatientDiagnosticName { get; set; }
    
    /// <summary>
    /// 次要诊断代码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "次要诊断代码字符长度不能超过100")]
    public string? SecondaryDiagnosticCode { get; set; }
    
    /// <summary>
    /// 次要诊断名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "次要诊断名称字符长度不能超过100")]
    public string? SecondaryDiagnosticName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>    
    [MaxLength(100, ErrorMessage = "入院途径字符长度不能超过100")]
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>    
    public DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 是否允许欠费
    /// </summary>    
    public bool? AllowArrears { get; set; }
    
    /// <summary>
    /// 欠费上限
    /// </summary>    
    public decimal? ArrearsLimit { get; set; }
    
    /// <summary>
    /// 担保人
    /// </summary>    
    [MaxLength(100, ErrorMessage = "担保人字符长度不能超过100")]
    public string? GuaranteePerson { get; set; }
    
    /// <summary>
    /// 新生儿出生体重g
    /// </summary>    
    public int? NewbornBirthWeight1 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>    
    public int? NewbornBirthWeight2 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>    
    public int? NewbornBirthWeight3 { get; set; }
    
    /// <summary>
    /// 新生儿入院体重g
    /// </summary>    
    public int? NewbornInpatientWeight { get; set; }
    
    /// <summary>
    /// 是否有医保卡
    /// </summary>    
    public int? HasMedicalInsurance { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    public int? Status { get; set; }
    
}

/// <summary>
/// 住院登记主键查询输入参数
/// </summary>
public class QueryByIdInpatientRegisterInput : DeleteInpatientRegisterInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataInpatientRegisterInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 住院登记数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportInpatientRegisterInput : BaseImportInput
{
    /// <summary>
    /// 患者ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者ID 文本
    /// </summary>
    [ImporterHeader(Name = "患者ID")]
    [ExporterHeader("患者ID", Format = "", Width = 25, IsBold = true)]
    public string PatientFkDisplayName { get; set; }
    
    /// <summary>
    /// 患者编号
    /// </summary>
    [ImporterHeader(Name = "患者编号")]
    [ExporterHeader("患者编号", Format = "", Width = 25, IsBold = true)]
    public string? PatientNo { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [ImporterHeader(Name = "患者姓名")]
    [ExporterHeader("患者姓名", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }
    
 
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [ImporterHeader(Name = "证件类型")]
    [ExporterHeader("证件类型", Format = "", Width = 25, IsBold = true)]
    public CardTypeEnum? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    [ImporterHeader(Name = "证件号码")]
    [ExporterHeader("证件号码", Format = "", Width = 25, IsBold = true)]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 保险号
    /// </summary>
    [ImporterHeader(Name = "保险号")]
    [ExporterHeader("保险号", Format = "", Width = 25, IsBold = true)]
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 就诊卡ID
    /// </summary>
    [ImporterHeader(Name = "就诊卡ID")]
    [ExporterHeader("就诊卡ID", Format = "", Width = 25, IsBold = true)]
    public long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [ImporterHeader(Name = "就诊卡号")]
    [ExporterHeader("就诊卡号", Format = "", Width = 25, IsBold = true)]
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [ImporterHeader(Name = "门诊号")]
    [ExporterHeader("门诊号", Format = "", Width = 25, IsBold = true)]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [ImporterHeader(Name = "住院号")]
    [ExporterHeader("住院号", Format = "", Width = 25, IsBold = true)]
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [ImporterHeader(Name = "住院流水号")]
    [ExporterHeader("住院流水号", Format = "", Width = 25, IsBold = true)]
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院次数
    /// </summary>
    [ImporterHeader(Name = "住院次数")]
    [ExporterHeader("住院次数", Format = "", Width = 25, IsBold = true)]
    public int? InpatientTimes { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    [ImporterHeader(Name = "病案号")]
    [ExporterHeader("病案号", Format = "", Width = 25, IsBold = true)]
    public string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 费别
    /// </summary>
    [ImporterHeader(Name = "费别")]
    [ExporterHeader("费别", Format = "", Width = 25, IsBold = true)]
    public   string? FeeName { get; set; }
    /// <summary>
    /// 医生ID
    /// </summary>
    [ImporterHeader(Name = "医生ID")]
    [ExporterHeader("医生ID", Format = "", Width = 25, IsBold = true)]
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    [ImporterHeader(Name = "医生姓名")]
    [ExporterHeader("医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    [ImporterHeader(Name = "科室ID")]
    [ExporterHeader("科室ID", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [ImporterHeader(Name = "科室名称")]
    [ExporterHeader("科室名称", Format = "", Width = 25, IsBold = true)]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 主治医生ID
    /// </summary>
    [ImporterHeader(Name = "主治医生ID")]
    [ExporterHeader("主治医生ID", Format = "", Width = 25, IsBold = true)]
    public long? MainDoctorId { get; set; }
    
    /// <summary>
    /// 主治医生姓名
    /// </summary>
    [ImporterHeader(Name = "主治医生姓名")]
    [ExporterHeader("主治医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? MainDoctorName { get; set; }
    
    /// <summary>
    /// 入院诊断代码
    /// </summary>
    [ImporterHeader(Name = "入院诊断代码")]
    [ExporterHeader("入院诊断代码", Format = "", Width = 25, IsBold = true)]
    public string? InpatientDiagnosticCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    [ImporterHeader(Name = "入院诊断名称")]
    [ExporterHeader("入院诊断名称", Format = "", Width = 25, IsBold = true)]
    public string? InpatientDiagnosticName { get; set; }
    
    /// <summary>
    /// 次要诊断代码
    /// </summary>
    [ImporterHeader(Name = "次要诊断代码")]
    [ExporterHeader("次要诊断代码", Format = "", Width = 25, IsBold = true)]
    public string? SecondaryDiagnosticCode { get; set; }
    
    /// <summary>
    /// 次要诊断名称
    /// </summary>
    [ImporterHeader(Name = "次要诊断名称")]
    [ExporterHeader("次要诊断名称", Format = "", Width = 25, IsBold = true)]
    public string? SecondaryDiagnosticName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    [ImporterHeader(Name = "入院途径")]
    [ExporterHeader("入院途径", Format = "", Width = 25, IsBold = true)]
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>
    [ImporterHeader(Name = "入院时间")]
    [ExporterHeader("入院时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 是否允许欠费
    /// </summary>
    [ImporterHeader(Name = "是否允许欠费")]
    [ExporterHeader("是否允许欠费", Format = "", Width = 25, IsBold = true)]
    public bool? AllowArrears { get; set; }
    
    /// <summary>
    /// 欠费上限
    /// </summary>
    [ImporterHeader(Name = "欠费上限")]
    [ExporterHeader("欠费上限", Format = "", Width = 25, IsBold = true)]
    public decimal? ArrearsLimit { get; set; }
    
    /// <summary>
    /// 担保人
    /// </summary>
    [ImporterHeader(Name = "担保人")]
    [ExporterHeader("担保人", Format = "", Width = 25, IsBold = true)]
    public string? GuaranteePerson { get; set; }
    
    /// <summary>
    /// 新生儿出生体重g
    /// </summary>
    [ImporterHeader(Name = "新生儿出生体重g")]
    [ExporterHeader("新生儿出生体重g", Format = "", Width = 25, IsBold = true)]
    public int? NewbornBirthWeight1 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    [ImporterHeader(Name = "新生儿出生体重单位g")]
    [ExporterHeader("新生儿出生体重单位g", Format = "", Width = 25, IsBold = true)]
    public int? NewbornBirthWeight2 { get; set; }
    
    /// <summary>
    /// 新生儿出生体重单位g
    /// </summary>
    [ImporterHeader(Name = "新生儿出生体重单位g")]
    [ExporterHeader("新生儿出生体重单位g", Format = "", Width = 25, IsBold = true)]
    public int? NewbornBirthWeight3 { get; set; }
    
    /// <summary>
    /// 新生儿入院体重g
    /// </summary>
    [ImporterHeader(Name = "新生儿入院体重g")]
    [ExporterHeader("新生儿入院体重g", Format = "", Width = 25, IsBold = true)]
    public int? NewbornInpatientWeight { get; set; }
    
    /// <summary>
    /// 是否有医保卡
    /// </summary>
    [ImporterHeader(Name = "是否有医保卡")]
    [ExporterHeader("是否有医保卡", Format = "", Width = 25, IsBold = true)]
    public int? HasMedicalInsurance { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
}
