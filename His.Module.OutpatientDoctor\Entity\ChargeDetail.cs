﻿using Admin.NET.Core;
namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 收费明细表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("charge_detail", "收费明细表")]
public class ChargeDetail : EntityTenantBaseData
{
    /// <summary>
    /// 收费主表Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_id", ColumnDescription = "收费主表Id")]
    public virtual long? ChargeId { get; set; }
    
    /// <summary>
    /// 单据Id(处方id)
    /// </summary>
    [SugarColumn(ColumnName = "billing_id", ColumnDescription = "单据Id(处方id)")]
    public virtual long? BillingId { get; set; }
    
    /// <summary>
    /// 单据号
    /// </summary>
    [SugarColumn(ColumnName = "billing_no", ColumnDescription = "单据号", Length = 64)]
    public virtual string? BillingNo { get; set; }
    
    /// <summary>
    /// 单据明细Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_detail_id", ColumnDescription = "单据明细Id")]
    public virtual long? BillingDetailId { get; set; }
    
    /// <summary>
    /// 单据类型
    /// </summary>
    [SugarColumn(ColumnName = "billing_type", ColumnDescription = "单据类型")]
    public virtual long? BillingType { get; set; }
    
    /// <summary>
    /// 单据时间
    /// </summary>
    [SugarColumn(ColumnName = "billing_time", ColumnDescription = "单据时间")]
    public virtual DateTime? BillingTime { get; set; }
    
    /// <summary>
    /// 执行科室Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_id", ColumnDescription = "执行科室Id")]
    public virtual long? ExecuteDeptId { get; set; }
    
    /// <summary>
    /// 执行医生Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_doctor_id", ColumnDescription = "执行医生Id")]
    public virtual long? ExecuteDoctorId { get; set; }
    
    /// <summary>
    /// 排除医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "execute_doctor_name", ColumnDescription = "排除医生姓名", Length = 64)]
    public virtual string? ExecuteDoctorName { get; set; }
    /// <summary>
    /// 执行状态
    /// </summary>
    [SugarColumn(ColumnName = "execute_status", ColumnDescription = "执行状态")]
    public YesNoEnum? ExecuteStatus { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [SugarColumn(ColumnName = "execute_time", ColumnDescription = "执行时间")]
    public DateTime? ExecuteTime { get; set; }
    /// <summary>
    /// 开单科室Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_id", ColumnDescription = "开单科室Id")]
    public virtual long? BillingDeptId { get; set; }
    
    /// <summary>
    /// 开单医生Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_id", ColumnDescription = "开单医生Id")]
    public virtual long? BillingDoctorId { get; set; }
    
    /// <summary>
    /// 开单医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_name", ColumnDescription = "开单医生姓名", Length = 64)]
    public virtual string? BillingDoctorName { get; set; }
    
    /// <summary>
    /// 项目Id
    /// </summary>
    [SugarColumn(ColumnName = "item_id", ColumnDescription = "项目Id")]
    public virtual long? ItemId { get; set; }
    
    /// <summary>
    /// 项目编号
    /// </summary>
    [SugarColumn(ColumnName = "item_code", ColumnDescription = "项目编号", Length = 64)]
    public virtual string? ItemCode { get; set; }
    
    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnName = "item_name", ColumnDescription = "项目名称", Length = 64)]
    public virtual string? ItemName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "规格", Length = 64)]
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 64)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量", Length = 16, DecimalDigits=4)]
    public virtual decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单次用量
    /// </summary>
    [SugarColumn(ColumnName = "single_dose", ColumnDescription = "单次用量", Length = 16, DecimalDigits=4)]
    public virtual decimal? SingleDose { get; set; }
    
    /// <summary>
    /// 单次用量单位
    /// </summary>
    [SugarColumn(ColumnName = "single_dose_unit", ColumnDescription = "单次用量单位", Length = 16)]
    public virtual string? SingleDoseUnit { get; set; }
    
    /// <summary>
    /// 给药途径Id
    /// </summary>
    [SugarColumn(ColumnName = "medication_routes_id", ColumnDescription = "给药途径Id")]
    public virtual long? MedicationRoutesId { get; set; }
    
    /// <summary>
    /// 频次Id
    /// </summary>
    [SugarColumn(ColumnName = "frequency_id", ColumnDescription = "频次Id")]
    public virtual long? FrequencyId { get; set; }
    
    /// <summary>
    /// 用药天数
    /// </summary>
    [SugarColumn(ColumnName = "medication_days", ColumnDescription = "用药天数")]
    public virtual Int16? MedicationDays { get; set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "单价", Length = 16, DecimalDigits=4)]
    public virtual decimal? Price { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 16, DecimalDigits=4)]
    public virtual decimal? Amount { get; set; }
    
    /// <summary>
    /// 草药数量
    /// </summary>
    [SugarColumn(ColumnName = "herbs_quantity", ColumnDescription = "草药数量")]
    public virtual int? HerbsQuantity { get; set; }
    
    /// <summary>
    /// 是否退费
    /// </summary>
    [SugarColumn(ColumnName = "withdrawal", ColumnDescription = "是否退费")]
    public virtual YesNoEnum? Withdrawal { get; set; }
    
    /// <summary>
    /// 退费时间
    /// </summary>
    [SugarColumn(ColumnName = "withdrawal_time", ColumnDescription = "退费时间")]
    public virtual DateTime? WithdrawalTime { get; set; }
    
    /// <summary>
    /// 支付方式1Id
    /// </summary>
    [SugarColumn(ColumnName = "pay_method1_id", ColumnDescription = "支付方式1Id")]
    public virtual long? PayMethod1Id { get; set; }
    
    /// <summary>
    /// 支付方式1金额
    /// </summary>
    [SugarColumn(ColumnName = "pay_amount1", ColumnDescription = "支付方式1金额", Length = 16, DecimalDigits=4)]
    public virtual decimal? PayAmount1 { get; set; }
    
    /// <summary>
    /// 支付方式2Id
    /// </summary>
    [SugarColumn(ColumnName = "pay_method2_id", ColumnDescription = "支付方式2Id")]
    public virtual long? PayMethod2Id { get; set; }
    
    /// <summary>
    /// 支付方式2金额
    /// </summary>
    [SugarColumn(ColumnName = "pay_amount2", ColumnDescription = "支付方式2金额", Length = 16, DecimalDigits=4)]
    public virtual decimal? PayAmount2 { get; set; }
    
    /// <summary>
    /// 药房Id
    /// </summary>
    [SugarColumn(ColumnName = "pharmacy_id", ColumnDescription = "药房Id")]
    public virtual long? PharmacyId { get; set; }
    
    /// <summary>
    /// 总支付金额
    /// </summary>
    [SugarColumn(ColumnName = "total_pay_amount", ColumnDescription = "总支付金额", Length = 16, DecimalDigits=4)]
    public virtual decimal? TotalPayAmount { get; set; }
    
    /// <summary>
    /// 收费类别Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_id", ColumnDescription = "收费类别Id")]
    public virtual long? ChargeCategoryId { get; set; }
    
    /// <summary>
    /// 收费类别编号
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_code", ColumnDescription = "收费类别编号")]
    public virtual string? ChargeCategoryCode { get; set; }
    

    /// <summary>
    /// 生产厂家
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer", ColumnDescription = "生产厂家", Length = 256)]
    public virtual string? Manufacturer { get; set; }
    /// <summary>
    /// 医保编号
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "医保编号", Length = 256)]
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 套餐Id
    /// </summary>
    [SugarColumn(ColumnName = "pack_id", ColumnDescription = "套餐Id")]
    public virtual long? PackId { get; set; }
    
    /// <summary>
    /// 套餐编码
    /// </summary>
    [SugarColumn(ColumnName = "pack_code", ColumnDescription = "套餐编码", Length = 64)]
    public virtual string? PackCode { get; set; }
    
    /// <summary>
    /// 套餐名称
    /// </summary>
    [SugarColumn(ColumnName = "pack_name", ColumnDescription = "套餐名称", Length = 64)]
    public virtual string? PackName { get; set; }
    
    /// <summary>
    /// 套餐数量
    /// </summary>
    [SugarColumn(ColumnName = "pack_number", ColumnDescription = "套餐数量")]
    public virtual Int16? PackNumber { get; set; }
    
    /// <summary>
    /// 套餐单价
    /// </summary>
    [SugarColumn(ColumnName = "pack_price", ColumnDescription = "套餐单价", Length = 16, DecimalDigits=4)]
    public virtual decimal? PackPrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual ChargeStatusEnum? Status { get; set; }
    
    /// <summary>
    /// 创建者部门Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建者部门Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建者部门名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
    
    /// <summary>
    /// 原始收费明细id
    /// </summary>
    [SugarColumn(ColumnName = "original_detail_id", ColumnDescription = "原始收费明细id")]
    public virtual long? OriginalDetailsId { get; set; }
    
            
    /// <summary>
    /// 退费原因
    /// </summary>
    [SugarColumn(ColumnName = "refund_reason", ColumnDescription = "退费原因")]
    public virtual string? RefundReason { get; set; }
    
}
