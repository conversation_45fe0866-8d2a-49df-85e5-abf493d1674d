﻿using Furion.DatabaseAccessor;
using His.Module.MedicalTech.Enum;
using His.Module.MedicalTech.OtherModelEntity;
namespace His.Module.MedicalTech.Service;

/// <summary>
/// 申请单处理服务 🧩
/// </summary>
[ApiDescriptionSettings(MedicalTechConst.GroupName, Order = 100)]
public class ApplyProcessService(
    SqlSugarRepository<Dispose> disposeRepository,
    SqlSugarRepository<LabTest> labTestRepository,
    SqlSugarRepository<Examination> examinationRepository,
    DisposeService disposeService,
    LabTestService labTestService,
    ExaminationService examinationService)
    : IDynamicApiController, ITransient
{

    /// <summary>
    /// 分页查询申请单 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询申请单")]
    [ApiDescriptionSettings(Name = "Page")]
    [HttpPost]
    public async Task<SqlSugarPagedList<ApplyProcessOutput>> Page(PageApplyProcessInput input)
    {
        // 查询 Dispose 表
        var disposeQuery = disposeRepository.AsTenant().QueryableWithAttr<Dispose>()
            .LeftJoin<Register>((a, b) => a.RegisterId == b.Id)
            .LeftJoin<FeeCategory>((a, b, c) => b.FeeId == c.Id)
            .WhereIF(!string.IsNullOrEmpty(input.ApplyNo), a => a.ApplyNo == input.ApplyNo)
            .WhereIF(!string.IsNullOrEmpty(input.VisitNo), a => a.VisitNo == input.VisitNo)
            .WhereIF(!string.IsNullOrEmpty(input.PatientName), a => a.PatientName.Contains(input.PatientName))
            .WhereIF(!string.IsNullOrEmpty(input.CardNo), a => a.CardNo == input.CardNo)
            .WhereIF(input.BillingDeptId.HasValue, a => a.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.StartTime.HasValue, a => a.BillingTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, a => a.BillingTime <= input.EndTime)
            .WhereIF(input.Status.HasValue, a => a.Status == input.Status)
            .WhereIF(input.Flag.HasValue, a => a.Flag == input.Flag)
            .Where((a, b, c) => a.ExecuteDeptId == long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value))
            .Select((a, b, c) => new ApplyProcessOutput
            {
                Id = a.Id, ItemId = a.ItemId, ItemCode = a.ItemCode, ItemName = a.ItemName,
                ApplyType = "Dispose", FeeName = c.Name
            }, true);

        // 查询 LabTest 表
        var labTestQuery = labTestRepository.AsTenant().QueryableWithAttr<LabTest>()
            .LeftJoin<Register>((a, b) => a.RegisterId == b.Id)
            .LeftJoin<FeeCategory>((a, b, c) => b.FeeId == c.Id)
            .WhereIF(!string.IsNullOrEmpty(input.ApplyNo), a => a.ApplyNo == input.ApplyNo)
            .WhereIF(!string.IsNullOrEmpty(input.VisitNo), a => a.VisitNo == input.VisitNo)
            .WhereIF(!string.IsNullOrEmpty(input.PatientName), a => a.PatientName.Contains(input.PatientName))
            .WhereIF(!string.IsNullOrEmpty(input.CardNo), a => a.CardNo == input.CardNo)
            .WhereIF(input.BillingDeptId.HasValue, a => a.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.StartTime.HasValue, a => a.BillingTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, a => a.BillingTime <= input.EndTime)
            .WhereIF(input.Status.HasValue, a => a.Status == input.Status)
            .WhereIF(input.Flag.HasValue, a => a.Flag == input.Flag)
            .Where((a, b, c) => a.ExecuteDeptId == long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value))
            .Select((a, b, c) => new ApplyProcessOutput
            {
                Id = a.Id, ItemId = a.ItemId, ItemCode = a.ItemCode, ItemName = a.ItemName,
                ApplyType = "LabTest", FeeName = c.Name
            }, true);

        // 查询 Examination 表
        var examinationQuery = examinationRepository.AsTenant()
            .QueryableWithAttr<Examination>()
            .LeftJoin<Register>((a, b) => a.RegisterId == b.Id)
            .LeftJoin<FeeCategory>((a, b, c) => b.FeeId == c.Id)
            .WhereIF(!string.IsNullOrEmpty(input.ApplyNo), a => a.ApplyNo == input.ApplyNo)
            .WhereIF(!string.IsNullOrEmpty(input.VisitNo), a => a.VisitNo == input.VisitNo)
            .WhereIF(!string.IsNullOrEmpty(input.PatientName), a => a.PatientName.Contains(input.PatientName))
            .WhereIF(!string.IsNullOrEmpty(input.CardNo), a => a.CardNo == input.CardNo)
            .WhereIF(input.BillingDeptId.HasValue, a => a.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.StartTime.HasValue, a => a.BillingTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, a => a.BillingTime <= input.EndTime)
            .WhereIF(input.Status.HasValue, a => a.Status == input.Status)
            .WhereIF(input.Flag.HasValue, a => a.Flag == input.Flag)
            .Where((a, b, c) => a.ExecuteDeptId == long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value))
            .Select((a, b, c) => new ApplyProcessOutput
            {
                Id = a.Id, ItemId = 0, ItemCode = "", ItemName = "",
                ApplyType = "Examination", FeeName = c.Name
            }, true);

        // 合并查询结果
        var combinedQuery =
            disposeRepository.Context.UnionAll(disposeQuery, labTestQuery, examinationQuery);

        // 分页返回结果
        return await combinedQuery.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取申请单详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取申请单详情")]
    [ApiDescriptionSettings(Name = "Detail")]
    [HttpPost]
    public async Task<List<ApplyProcessDetailOutput>> Detail(QueryByIdApplyProcessInput input)
    {
        // 定义 ApplyType 与对应服务方法的映射
        var serviceActions = new Dictionary<string, Func<Task<List<ApplyProcessDetailOutput>>>>
        {
            {
                "Dispose", async () =>
                {
                    var result = await disposeService.Detail(new QueryByIdDisposeInput
                                 {
                                     Id = input.Id
                                 })
                                 ?? throw Oops.Oh(ErrorCodeEnum.D1002);
                    var detail = result.Adapt<ApplyProcessDetailOutput>();
                    detail.ApplyProcessPackageItemOutputs =
                        result.DisposePackageItem.Adapt<List<ApplyProcessPackageItemOutput>>();
                    return [detail];
                }
            },
            {
                "LabTest", async () =>
                {
                    var result = await labTestService.Detail(new QueryByIdLabTestInput
                                 {
                                     Id = input.Id
                                 })
                                 ?? throw Oops.Oh(ErrorCodeEnum.D1002);
                    var detail = result.Adapt<ApplyProcessDetailOutput>();
                    detail.ApplyProcessPackageItemOutputs =
                        result.LabTestPackageItem.Adapt<List<ApplyProcessPackageItemOutput>>();
                    return [detail];
                }
            },
            {
                "Examination", async () =>
                {
                    var result =
                        await examinationService.Detail(new QueryByIdExaminationInput
                        {
                            Id = input.Id
                        })
                        ?? throw Oops.Oh(ErrorCodeEnum.D1002);
                    return result.ExaminationDetails.Select(item =>
                    {
                        var detail = item.Adapt<ApplyProcessDetailOutput>();
                        detail.ApplyProcessPackageItemOutputs =
                            item.ExaminationPackageItem.Adapt<List<ApplyProcessPackageItemOutput>>();
                        return detail;
                    }).ToList();
                }
            }
        };

        // 根据 ApplyType 执行对应的服务方法
        if (serviceActions.TryGetValue(input.ApplyType, out var action))
        {
            return await action();
        }

        throw Oops.Oh(MedicalTechErrorCodeEnum.MT0005);
    }

    /// <summary>
    /// 设置申请单状态 🔄
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置申请单状态")]
    [ApiDescriptionSettings(Name = "SetStatus")]
    [HttpPost]
    public async Task SetStatus(SetApplyProcessStatusInput input)
    {
        // 定义 ApplyType 与对应服务方法的映射
        var serviceActions = new Dictionary<string, Func<Task>>
        {
            {
                "Dispose", () => disposeService.SetStatus(new SetDisposeStatusInput
                {
                    Id = input.Id, Status = input.Status
                })
            },
            {
                "LabTest", () => labTestService.SetStatus(new SetLabTestStatusInput
                {
                    Id = input.Id, Status = input.Status
                })
            },
            {
                "Examination", () => examinationService.SetStatus(new SetExaminationStatusInput
                {
                    Id = input.Id, Status = input.Status
                })
            }
        };

        // 根据 ApplyType 执行对应的服务方法
        if (serviceActions.TryGetValue(input.ApplyType, out var action))
        {
            await action();
        }
        else
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0005);
        }
    }

    /// <summary>
    /// 申请单收费 💰
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("申请单收费")]
    [ApiDescriptionSettings(Name = "Charge")]
    [HttpPost]
    [UnitOfWork]
    public async Task Charge(ChargeApplyProcessInput input)
    {
        // 校验所有申请单是否属于同一患者
        await ValidatePatientId(input);
        // 发票号
        var invoiceNumber = await disposeRepository.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('outpatient_doctor.charge_main_in_num_seq')As varchar),8,'0')");
        // 定义 ApplyType 与对应服务方法的映射
        var chargeActions = new Dictionary<string, Func<long, Task>>
        {
            {
                "Dispose", id => disposeService.Charge(new ChargeDisposeInput
                {
                    Id = id, InvoiceNumber = invoiceNumber
                })
            },
            {
                "LabTest", id => labTestService.Charge(new ChargeLabTestInput
                {
                    Id = id, InvoiceNumber = invoiceNumber
                })
            },
            {
                "Examination", id => examinationService.Charge(new ChargeExaminationInput
                {
                    Id = id, InvoiceNumber = invoiceNumber
                })
            }
        };

        // 处理所有申请单的收费
        foreach (var applyDetail in input.ApplyDetails)
        {
            if (chargeActions.TryGetValue(applyDetail.ApplyType, out var chargeAction))
            {
                await chargeAction(applyDetail.Id);
            }
            else
            {
                throw Oops.Oh(MedicalTechErrorCodeEnum.MT0005); // 未知的 ApplyType
            }
        }
    }

    /// <summary>
    /// 校验所有申请单是否属于同一患者
    /// </summary>
    /// <param name="input">收费输入参数</param>
    /// <returns></returns>
    private async Task ValidatePatientId(ChargeApplyProcessInput input)
    {
        if (input.ApplyDetails == null || input.ApplyDetails.Count == 0)
        {
            return;
        }

        // 获取所有申请单的患者ID
        HashSet<long> patientIds = [];

        foreach (var detail in input.ApplyDetails)
        {
            long patientId = 0;

            switch (detail.ApplyType)
            {
                case "Dispose":
                    var dispose = await disposeRepository.GetFirstAsync(d => d.Id == detail.Id);
                    if (dispose != null)
                    {
                        patientId = dispose.PatientId;
                    }
                    break;
                case "LabTest":
                    var labTest = await labTestRepository.GetFirstAsync(l => l.Id == detail.Id);
                    if (labTest != null)
                    {
                        patientId = labTest.PatientId;
                    }
                    break;
                case "Examination":
                    var examination = await examinationRepository.GetFirstAsync(e => e.Id == detail.Id);
                    if (examination != null)
                    {
                        patientId = examination.PatientId;
                    }
                    break;
                default:
                    throw Oops.Oh(MedicalTechErrorCodeEnum.MT0005);
            }

            if (patientId <= 0)
            {
                continue;
            }
            patientIds.Add(patientId);

            // 校验是否与输入的患者ID一致
            if (input.PatientId > 0 && patientId != input.PatientId)
            {
                throw Oops.Oh(MedicalTechErrorCodeEnum.MT0007);
            }
        }

        // 如果有多个不同的患者ID，抛出异常
        if (patientIds.Count > 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0007);
        }
    }
}