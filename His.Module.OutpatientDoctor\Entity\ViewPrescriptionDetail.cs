﻿using His.Module.OutpatientDoctor.Service.OutpatientCharge.Dto;
namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 处方明细统一视图实体
/// </summary>
[SugarTable("view_prescription_detail", "处方明细统一视图")]
[Tenant("1300000000005")]
public class ViewPrescriptionDetail
{
    /// <summary>
    /// 主表ID
    /// </summary>
    [SugarColumn(ColumnName = "main_id", ColumnDescription = "主表ID")]
    public long MainId { get; set; }

    /// <summary>
    /// 业务类型（lab_test/examination/prescription/dispose）
    /// </summary>
    [SugarColumn(ColumnName = "business_type", ColumnDescription = "业务类型", Length = 32)]
    public string BusinessType { get; set; }

    /// <summary>
    /// 申请单号/处方号
    /// </summary>
    [SugarColumn(ColumnName = "apply_no", ColumnDescription = "申请单号", Length = 64)]
    public string ApplyNo { get; set; }

    /// <summary>
    /// 就诊ID
    /// </summary>
    [SugarColumn(ColumnName = "visit_id", ColumnDescription = "就诊ID")]
    public long VisitId { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 64)]
    public string VisitNo { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 100)]
    public string PatientName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public int Status { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    [SugarColumn(ColumnName = "billing_time", ColumnDescription = "开单时间")]
    public DateTime BillingTime { get; set; }

    /// <summary>
    /// 开单科室ID
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_id", ColumnDescription = "开单科室ID")]
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_name", ColumnDescription = "开单科室名称", Length = 100)]
    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生ID
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_id", ColumnDescription = "开单医生ID")]
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_name", ColumnDescription = "开单医生名称", Length = 100)]
    public string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行科室ID
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_id", ColumnDescription = "执行科室ID")]
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_name", ColumnDescription = "执行科室名称", Length = 100)]
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 明细ID
    /// </summary>
    [SugarColumn(ColumnName = "detail_id", ColumnDescription = "明细ID")]
    public long? DetailId { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    [SugarColumn(ColumnName = "item_id", ColumnDescription = "项目ID")]
    public long ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [SugarColumn(ColumnName = "item_code", ColumnDescription = "项目编码", Length = 100)]
    public string ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnName = "item_name", ColumnDescription = "项目名称", Length = 200)]
    public string ItemName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "规格", ColumnDataType = "text")]
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 50)]
    public string Unit { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "单价", Length = 16, DecimalDigits = 4)]
    public decimal Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量", Length = 16, DecimalDigits = 4)]
    public decimal Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 16, DecimalDigits = 4)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 医保编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "医保编码", Length = 100)]
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>
    [SugarColumn(ColumnName = "nationalstandard_code", ColumnDescription = "国标编码", Length = 100)]
    public string? NationalstandardCode { get; set; }

    /// <summary>
    /// 收费类别ID
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_id", ColumnDescription = "收费类别ID")]
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 收费类别名称
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_name", ColumnDescription = "收费类别名称")]
    public string? ChargeCategoryName { get; set; }

    /// <summary>
    /// 是否套餐（1是套餐，2不是套餐）
    /// </summary>
    [SugarColumn(ColumnName = "is_package", ColumnDescription = "是否套餐")]
    public int IsPackage { get; set; }

    /// <summary>
    /// 自费比例
    /// </summary>
    [SugarColumn(ColumnName = "self_pay_ratio", ColumnDescription = "自费比例", Length = 16, DecimalDigits = 4)]
    public decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 套餐项目JSON（如果是套餐则包含子项目）
    /// </summary>
    [SugarColumn(ColumnName = "package_items_json", ColumnDescription = "套餐项目JSON", IsJson = true)]
    public List<PackageSubItemDto>? PackageItemsJson { get; set; }

    /// <summary>
    /// 租户ID
    /// </summary>
    [SugarColumn(ColumnName = "tenant_id", ColumnDescription = "租户ID")]
    public long? TenantId { get; set; }
}
