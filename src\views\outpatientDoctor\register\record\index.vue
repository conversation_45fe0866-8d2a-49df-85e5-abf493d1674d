<template>
	<div class="outpatientRegisterRecord-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.queryParams" ref="queryForm" labelWidth="80" :inline="true">
				<el-form-item label="门诊流水号">
					<el-input v-model="state.queryParams.id" clearable="" placeholder="请输入门诊流水号" />
				</el-form-item>
				<el-form-item label="患者姓名">
					<el-input v-model="state.queryParams.name" clearable="" placeholder="请输入患者姓名" />
				</el-form-item>
				<el-form-item label="身份证号">
					<el-input v-model="state.queryParams.idCardNo" clearable="" placeholder="请输入身份证号" />
				</el-form-item>
				<el-form-item label="就诊日期">
					<el-date-picker
						v-model="state.dateTimeRange"
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						:default-time="['2000-01-01 00:00:00', '2000-01-01 23:59:59']"
						value-format="YYYY-MM-DD HH:mm:ss"
						style="width: 380px"
						@change="handleDateTimeRangeChange"
					/>
				</el-form-item>
				<el-form-item>
					<el-button-group style="display: flex; align-items: center">
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'register:page'"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="state.tableData" style="width: 100%" v-loading="state.loading" tooltip-effect="light" row-key="id" border="">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="visitNo" label="门诊流水号" show-overflow-tooltip="" />
				<el-table-column prop="patientName" label="患者姓名" show-overflow-tooltip="" />
				<el-table-column prop="sex" label="性别" show-overflow-tooltip="">
					<template #default="scope">
						<g-sys-dict v-model="scope.row.sex" code="GenderEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="age" label="年龄" show-overflow-tooltip="" />
				<el-table-column prop="deptName" label="科室" show-overflow-tooltip="" />
				<el-table-column prop="doctorName" label="医生" show-overflow-tooltip="" />
				<el-table-column prop="feeName" label="费别" show-overflow-tooltip="" />
				<el-table-column prop="regCategory" label="号别" show-overflow-tooltip="" />
				<el-table-column prop="status" label="状态" show-overflow-tooltip="">
					<template #default="scope">
						<g-sys-dict v-model="scope.row.status" code="RegStatusEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="actualChargeFee" label="实收费用" show-overflow-tooltip="" />
				<!-- <el-table-column prop="cardNo" label="卡号" show-overflow-tooltip="" /> -->
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('register:update') || auth('register:delete')">
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditRegister(scope.row)" v-auth="'register:update'"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text="" type="primary" @click="delRegister(scope.row)" v-auth="'register:delete'"> 退号 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				size="small"
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>
	</div>
</template>
<script lang="ts" setup name="outpatientRegisterRecord">
import { reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
import { formatDate } from '/@/utils/formatTime';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import { useRouter } from 'vue-router';

import { useRegisterApi } from '/@/api/outpatientDoctor/register';

// API服务
const registerApi = useRegisterApi();
const router = useRouter();
const state = reactive({
	loading: false,
	tableData: [] as any,
	dateTimeRange: [] as any, // 日期时间范围，使用 any 类型避免类型错误
	queryParams: {
		id: undefined,
		name: undefined,
		idCardNo: undefined,
		startTime: undefined as string | undefined,
		endTime: undefined as string | undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0 as any,
	},
});

// 处理日期时间范围变化
const handleDateTimeRangeChange = (val: any) => {
	if (val && val.length === 2) {
		state.queryParams.startTime = val[0];
		state.queryParams.endTime = val[1];
	} else {
		state.queryParams.startTime = undefined;
		state.queryParams.endTime = undefined;
	}
};
// 页面加载时
onMounted(async () => {
	// 设置当天日期作为默认就诊日期范围
	const today = new Date();
	const startOfDay = new Date(today);
	startOfDay.setHours(0, 0, 0, 0);
	const endOfDay = new Date(today);
	endOfDay.setHours(23, 59, 59, 999);

	// 格式化日期
	const startTimeStr = formatDate(startOfDay, 'YYYY-mm-dd HH:MM:SS');
	const endTimeStr = formatDate(endOfDay, 'YYYY-mm-dd HH:MM:SS');

	// 设置日期范围
	state.dateTimeRange = [startTimeStr, endTimeStr];
	state.queryParams.startTime = startTimeStr;
	state.queryParams.endTime = endTimeStr;

	await handleQuery();
});

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	var res = await registerApi.page(Object.assign(state.queryParams, state.tableParams));
	state.tableData = res.data.result?.items ?? [];
	state.tableParams.total = res.data.result?.total;
	state.loading = false;
};
// 重置操作
const resetQuery = () => {
	state.queryParams.id = undefined;
	state.queryParams.name = undefined;
	state.queryParams.idCardNo = undefined;

	// 重新设置当天日期作为默认就诊日期范围
	const today = new Date();
	const startOfDay = new Date(today);
	startOfDay.setHours(0, 0, 0, 0);
	const endOfDay = new Date(today);
	endOfDay.setHours(23, 59, 59, 999);

	// 格式化日期 - 使用大写的 MM 和 DD 以及 HH:mm:ss 格式
	const startTimeStr = formatDate(startOfDay, 'YYYY-mm-dd HH:MM:SS');
	const endTimeStr = formatDate(endOfDay, 'YYYY-mm-dd HH:MM:SS');

	// 设置日期范围
	state.dateTimeRange = [startTimeStr, endTimeStr];
	state.queryParams.startTime = startTimeStr;
	state.queryParams.endTime = endTimeStr;

	handleQuery();
};

// 打开编辑页面
const openEditRegister = (row: any) => {
	// 跳转到创建页面，并传递编辑数据
	router.push({
		path: '/outpatientRegister/create',
		query: { id: row.id, type: 'edit' },
	});
};
//退号操作
const delRegister = (row: any) => {
	ElMessageBox.confirm(`确定要退号吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await registerApi.refund({ id: row.id });
			handleQuery();
			ElMessage.success('退号成功');
		})
		.catch(() => {});
};
// 改变页面容量
const handleSizeChange = (val: number) => {
	state.tableParams.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	state.tableParams.page = val;
	handleQuery();
};
</script>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
