import { useBaseApi } from '/@/api/base';

// 皮试结果接口服务
export const useSkinTestResultApi = () => {
	const baseApi = useBaseApi('skinTestResult');
	return {
		// 分页查询需要进行皮试的处方
		page: baseApi.page,
		// 查看处方详细
		detail: baseApi.detail,
		// 录入皮试结果
		add: baseApi.add,
		// 批量录入皮试结果
		batchAdd: (data: any) => baseApi.post('batchAdd', data),
		// 撤销皮试结果
		cancel: (data: any) => baseApi.post('cancel', data),
		// 分页查询皮试结果记录
		pageRecord: (data: any) => baseApi.post('pageRecord', data),
	};
};
