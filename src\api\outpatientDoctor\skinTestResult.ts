import { useBaseApi } from '/@/api/base';

// 皮试结果接口服务
export const useSkinTestResultApi = () => {
	const baseApi = useBaseApi('skinTestResult');
	return {
		// 分页查询需要进行皮试的处方
		page: baseApi.page,
		// 查看处方详细
		detail: baseApi.detail,
		// 录入皮试结果
		add: baseApi.add,
		// 批量录入皮试结果
		batchAdd: (data: any) => baseApi.post('batchAdd', data),
		// 撤销皮试结果
		cancel: (data: any) => baseApi.post('cancel', data),
		// 分页查询皮试结果记录
		pageRecord: (data: any) => baseApi.post('pageRecord', data),
	};
};

// 皮试结果录入输入参数
export interface AddSkinTestResultInput {
	// 处方详情Id
	prescriptionDetailId: number;
	// 皮试结果
	skinTestResult: string;
	// 皮试时间
	skinTestTime?: string;
	// 皮试方法
	skinTestMethod?: string;
	// 皮试剂量
	skinTestDose?: string;
	// 反应描述
	reactionDescription?: string;
	// 备注
	remark?: string;
}

// 批量皮试结果录入输入参数
export interface BatchAddSkinTestResultInput {
	// 处方详情Id列表
	prescriptionDetailIds: number[];
	// 皮试结果 1阴性 2阳性 3可疑
	skinTestResult: number;
	// 皮试时间
	skinTestTime?: string;
	// 皮试方法
	skinTestMethod?: string;
	// 皮试剂量
	skinTestDose?: string;
	// 反应描述
	reactionDescription?: string;
	// 备注
	remark?: string;
}

// 撤销皮试结果输入参数
export interface CancelSkinTestResultInput {
	// 皮试结果Id
	id: number;
	// 撤销原因
	cancelReason: string;
}

// 皮试结果查询输入参数
export interface PageSkinTestResultRecordInput {
	// 页码
	page: number;
	// 页大小
	pageSize: number;
	// 患者姓名
	patientName?: string;
	// 就诊号
	visitNo?: string;
	// 药品名称
	drugName?: string;
	// 皮试结果
	skinTestResult?: string;
	// 皮试时间范围
	skinTestTimeRange?: string[];
	// 状态
	status?: number;
}

// 分页查询需要皮试的处方输入参数
export interface PageSkinTestResultInput {
	// 页码
	page: number;
	// 页大小
	pageSize: number;
	// 处方时间范围
	prescriptionTimeRange?: string[];
	// 是否皮试
	isSkinTest?: number;
	// 开单科室
	billingDeptId?: number;
}

// 处方详情输入参数
export interface SkinTestResultDetailInput {
	// 处方Id
	id: number;
}

// 皮试结果输出
export interface SkinTestResultOutput {
	// 处方Id
	id: number;
	// 处方号
	prescriptionNo: string;
	// 处方时间
	prescriptionTime: string;
	// 患者Id
	patientId: number;
	// 患者姓名
	patientName: string;
	// 性别
	gender: string;
	// 年龄
	age: number;
	// 年龄单位
	ageUnit: string;
	// 开单科室名称
	billingDeptName: string;
	// 开单医生姓名
	billingDoctorName: string;
	// 需要皮试的药品数量
	skinTestDrugCount: number;
	// 已完成皮试的药品数量
	completedSkinTestCount: number;
	// 皮试状态 0未开始 1进行中 2已完成
	skinTestStatus: number;
}

// 皮试结果详情输出
export interface SkinTestResultDetailOutput {
	// 处方详情Id
	id: number;
	// 处方Id
	prescriptionId: number;
	// 药品Id
	drugId: number;
	// 药品名称
	drugName: string;
	// 药品规格
	drugSpec: string;
	// 数量
	quantity: number;
	// 单位
	unit: string;
	// 是否需要皮试
	isSkinTest: number;
	// 皮试结果
	skinTestResult?: string;
	// 皮试时间
	skinTestTime?: string;
	// 皮试人员姓名
	skinTestStaffName?: string;
	// 皮试方法
	skinTestMethod?: string;
	// 皮试剂量
	skinTestDose?: string;
	// 反应描述
	reactionDescription?: string;
	// 备注
	remark?: string;
	// 皮试结果记录Id
	skinTestResultId?: number;
	// 皮试结果状态
	skinTestResultStatus?: number;
}

// 皮试结果记录输出
export interface SkinTestResultRecordOutput {
	// 皮试结果Id
	id: number;
	// 处方详情Id
	prescriptionDetailId: number;
	// 处方号
	prescriptionNo: string;
	// 患者姓名
	patientName: string;
	// 就诊号
	visitNo: string;
	// 药品名称
	drugName: string;
	// 药品规格
	drugSpec: string;
	// 皮试结果
	skinTestResult: string;
	// 皮试时间
	skinTestTime: string;
	// 皮试人员姓名
	skinTestStaffName: string;
	// 皮试方法
	skinTestMethod?: string;
	// 皮试剂量
	skinTestDose?: string;
	// 反应描述
	reactionDescription?: string;
	// 备注
	remark?: string;
	// 状态
	status: number;
	// 撤销时间
	cancelTime?: string;
	// 撤销人员姓名
	cancelStaffName?: string;
	// 撤销原因
	cancelReason?: string;
}

// 皮试结果枚举
export enum SkinTestResultEnum {
	// 阴性
	Negative = 1,
	// 阳性
	Positive = 2,
	// 可疑
	Suspicious = 3,
}

// 皮试结果状态枚举
export enum SkinTestResultStatusEnum {
	// 有效
	Valid = 1,
	// 撤销
	Cancelled = 2,
}

// 皮试状态枚举
export enum SkinTestStatusEnum {
	// 未开始
	NotStarted = 0,
	// 进行中
	InProgress = 1,
	// 已完成
	Completed = 2,
}
