﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
	  <NoWarn>1701;1702;1591;8632</NoWarn>
	  <DocumentationFile></DocumentationFile>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <GenerateDocumentationFile>True</GenerateDocumentationFile>
	  <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.NET.Core\Admin.NET.Core.csproj" />
    <ProjectReference Include="..\His.Module.OutpatientDoctor.Api\His.Module.OutpatientDoctor.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Configuration\Insurance.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Interface\" />
  </ItemGroup>

</Project>
