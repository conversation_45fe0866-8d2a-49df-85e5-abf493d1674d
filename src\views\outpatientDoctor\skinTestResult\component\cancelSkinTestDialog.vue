<script lang="ts" setup name="cancelSkinTestDialog">
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useSkinTestResultApi } from '/@/api/outpatientDoctor/skinTestResult';

// API服务
const skinTestResultApi = useSkinTestResultApi();

// 组件事件
const emit = defineEmits(['success']);

// 弹窗状态
const dialogVisible = ref(false);
const loading = ref(false);

// 表单数据
const formData = reactive({
	id: 0,
	cancelReason: '',
});

// 皮试结果信息
const skinTestInfo = reactive({
	patientName: '',
	prescriptionNo: '',
	drugName: '',
	drugSpec: '',
	skinTestResult: '',
	skinTestTime: '',
	skinTestStaffName: '',
	skinTestMethod: '',
	skinTestDose: '',
	reactionDescription: '',
	remark: '',
});

// 表单引用
const formRef = ref();

// 皮试结果选项映射
const skinTestResultMap: Record<string, string> = {
	'1': '阴性',
	'2': '阳性',
	'3': '可疑',
};

// 表单验证规则
const formRules = {
	cancelReason: [
		{ required: true, message: '请输入撤销原因', trigger: 'blur' },
		{ min: 2, max: 200, message: '撤销原因长度在 2 到 200 个字符', trigger: 'blur' }
	],
};

// 打开弹窗
const open = (detailRow: any) => {
	// 重置表单
	resetForm();
	
	// 设置表单数据
	formData.id = detailRow.skinTestResultId;
	
	// 设置皮试结果信息
	Object.assign(skinTestInfo, {
		patientName: detailRow.patientName || '',
		prescriptionNo: detailRow.prescriptionNo || '',
		drugName: detailRow.drugName || '',
		drugSpec: detailRow.drugSpec || '',
		skinTestResult: skinTestResultMap[detailRow.skinTestResult] || detailRow.skinTestResult,
		skinTestTime: detailRow.skinTestTime || '',
		skinTestStaffName: detailRow.skinTestStaffName || '',
		skinTestMethod: detailRow.skinTestMethod || '',
		skinTestDose: detailRow.skinTestDose || '',
		reactionDescription: detailRow.reactionDescription || '',
		remark: detailRow.remark || '',
	});
	
	dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
	Object.assign(formData, {
		id: 0,
		cancelReason: '',
	});
	
	Object.assign(skinTestInfo, {
		patientName: '',
		prescriptionNo: '',
		drugName: '',
		drugSpec: '',
		skinTestResult: '',
		skinTestTime: '',
		skinTestStaffName: '',
		skinTestMethod: '',
		skinTestDose: '',
		reactionDescription: '',
		remark: '',
	});
	
	formRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = async () => {
	try {
		// 表单验证
		await formRef.value?.validate();
		
		// 二次确认
		await ElMessageBox.confirm(
			'撤销后将清空该药品的皮试结果，确定要撤销吗？',
			'确认撤销',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);
		
		loading.value = true;
		
		// 调用API
		await skinTestResultApi.cancel(formData);
		
		ElMessage.success('皮试结果撤销成功');
		dialogVisible.value = false;
		emit('success');
	} catch (error) {
		if (error !== 'cancel') {
			console.error('撤销皮试结果失败:', error);
			ElMessage.error('撤销皮试结果失败');
		}
	} finally {
		loading.value = false;
	}
};

// 取消
const handleCancel = () => {
	dialogVisible.value = false;
};

// 暴露方法
defineExpose({
	open,
});
</script>

<template>
	<el-dialog
		v-model="dialogVisible"
		title="撤销皮试结果"
		width="600px"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
	>
		<!-- 皮试结果信息 -->
		<el-card class="info-card" shadow="never">
			<template #header>
				<span style="font-weight: bold; color: #e6a23c;">当前皮试结果信息</span>
			</template>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="患者姓名">{{ skinTestInfo.patientName }}</el-descriptions-item>
				<el-descriptions-item label="处方号">{{ skinTestInfo.prescriptionNo }}</el-descriptions-item>
				<el-descriptions-item label="药品名称">{{ skinTestInfo.drugName }}</el-descriptions-item>
				<el-descriptions-item label="药品规格">{{ skinTestInfo.drugSpec }}</el-descriptions-item>
				<el-descriptions-item label="皮试结果">
					<el-tag :type="skinTestInfo.skinTestResult === '阴性' ? 'success' : skinTestInfo.skinTestResult === '阳性' ? 'danger' : 'warning'">
						{{ skinTestInfo.skinTestResult }}
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="皮试时间">{{ skinTestInfo.skinTestTime }}</el-descriptions-item>
				<el-descriptions-item label="皮试人员">{{ skinTestInfo.skinTestStaffName }}</el-descriptions-item>
				<el-descriptions-item label="皮试方法">{{ skinTestInfo.skinTestMethod || '无' }}</el-descriptions-item>
				<el-descriptions-item label="皮试剂量" :span="2">{{ skinTestInfo.skinTestDose || '无' }}</el-descriptions-item>
				<el-descriptions-item label="反应描述" :span="2">{{ skinTestInfo.reactionDescription || '无' }}</el-descriptions-item>
				<el-descriptions-item label="备注" :span="2">{{ skinTestInfo.remark || '无' }}</el-descriptions-item>
			</el-descriptions>
		</el-card>

		<!-- 撤销原因表单 -->
		<el-form
			ref="formRef"
			:model="formData"
			:rules="formRules"
			label-width="100px"
			style="margin-top: 20px"
		>
			<el-form-item label="撤销原因" prop="cancelReason">
				<el-input
					v-model="formData.cancelReason"
					type="textarea"
					:rows="4"
					placeholder="请输入撤销原因（必填）"
					maxlength="200"
					show-word-limit
				/>
			</el-form-item>
		</el-form>

		<!-- 警告提示 -->
		<el-alert
			title="注意：撤销后将清空该药品的皮试结果，如需重新录入请点击"录入皮试"按钮"
			type="warning"
			:closable="false"
			style="margin-top: 20px"
		/>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleCancel">取消</el-button>
				<el-button type="danger" :loading="loading" @click="handleSubmit">
					确定撤销
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<style scoped>
.info-card {
	margin-bottom: 20px;
}

.dialog-footer {
	text-align: right;
}

:deep(.el-descriptions__label) {
	font-weight: bold;
}
</style>
