﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.Financial.Api.MedicalCard;
using His.Module.Financial.Api.MedicalCard.Dto;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.OutpatientDoctor.Api.Prescription.Dto;
using His.Module.OutpatientDoctor.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 退费审核表服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class RefundAuditService(
    SqlSugarRepository<RefundApply> refundApplyRep,
    SqlSugarRepository<RefundAuditFlow> refundAuditFlowRep,
    SqlSugarRepository<RefundAudit> refundAuditRep,
PrescriptionService prescriptionService,
    ChargeService chargeService,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _sqlSugarClient = sqlSugarClient;

    /// <summary>
    /// 获取审核记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取审核记录")]
    [ApiDescriptionSettings(Name = "List"), HttpPost]
    public async Task<List<RefundAuditOutput>> List(RefundAuditQueryInput input)
    {
        var query = refundAuditRep.AsQueryable()
            .Where(u => u.ApplyId == input.ApplyId)
            .OrderBy(u => u.FlowSort)
            .Select<RefundAuditOutput>();
        return await query.ToListAsync();
    }


    /// <summary>
    /// 初始化审核记录
    /// </summary>
    [NonAction]
    public async Task InitAuditRecord(InitAuditRecordDto dto, ChargeMain charge)
    {
        List<RefundAuditFlow> flows = [];
        if (charge.BillingType == "Prescription")
        {
            if (charge.ExecuteStatus == YesNoEnum.Y) //  已发药
            {
                flows = await GetFlowByCode("PrescriptionIsExecute");
            }
            else
            {
                flows = await GetFlowByCode("Prescription");
            }
        }

        // 将退费流程保存到审核数据库中

        for (var i = 0; i < flows.Count; i++)
        {
            var flow = flows[i];
            var refundAudit = new RefundAudit()
            {
                AuditStatus = null,
                ApplyId = dto.ApplyId,
                FlowId = flow.Id,
                FlowSort = flow.FlowSort,
                FlowRoleId = flow.RoleId,
                FlowRoleCode = flow.RoleCode,
                FlowRoleName = flow.RoleName,
                FlowUserId = flow.UserId,
                FlowUserName = flow.UserName,
            };
            if (i == 0)
            {
                // 默认待审核  审核后设置下一条为0 
                refundAudit.AuditStatus = 0;
            }

            refundAudit.ApplyId = dto.ApplyId;
            await refundAuditRep.InsertAsync(refundAudit);
        }
    }

    private async Task<List<RefundAuditFlow>> GetFlowByCode(string code)
    {
        return await refundAuditFlowRep.AsQueryable()
                .Where(u => u.Code == code)
                .OrderBy(u => u.FlowSort)
                .ToListAsync()
            ;
    }

    /// <summary>
    /// 获取退费审核表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取退费审核表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<RefundAudit> Detail([FromQuery] QueryByIdRefundAuditInput input)
    {
        return await refundAuditRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加退费审核表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加退费审核表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost, UnitOfWork]
    public async Task<long> Add(AddRefundAuditInput input)
    {
        var list = await refundAuditRep.AsQueryable()
            .Where(u => u.ApplyId == input.ApplyId)
            .ToListAsync();
        var waitAudit = list.FirstOrDefault(u => u.AuditStatus == 0);
        // 下一个
        var nextAudit = list.Where(u => u.AuditStatus == null).OrderBy(u => u.FlowSort).FirstOrDefault();
        // 修改当前审核状态

        await refundAuditRep.UpdateAsync(
            u =>
                new RefundAudit()
                {
                    AuditStatus = input.AuditStatus,
                    AuditReason = input.AuditReason,
                    AuditTime = DateTime.Now,
                    AuditUserId = long.Parse(App.User.FindFirst(ClaimConst.UserId).Value),
                    AuditUserName = App.User.FindFirst(ClaimConst.RealName).Value,
                    AuditDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value),
                    AuditDeptCode = App.User.FindFirst(ClaimConst.OrgCode).Value,
                    AuditDeptName = App.User.FindFirst(ClaimConst.OrgName).Value,
                    UpdateTime = DateTime.Now,
                }, u => u.Id == waitAudit.Id);

       
        
        // 修改下一个的审核状态
        if (nextAudit != null)
        {
            await refundAuditRep.UpdateAsync(
                u =>
                    new RefundAudit() { AuditStatus = 0, }, u => u.Id == nextAudit.Id);

            await UpdateRefundStatus(input.ApplyId, input.AuditStatus ?? 0); // 审核中
        }
        else
        {
            // 当前退费申请审核完成
            await refundApplyRep.UpdateAsync(u =>
                    new RefundApply() { AuditStatus = input.AuditStatus, Status = 2 }, u => u.Id == input.ApplyId
            );
            //退费 
            var apply = await refundApplyRep.GetByIdAsync(input.ApplyId);
            var charge = await chargeService.Get(apply.ChargeId ?? 0);
            
            if (apply.BillingType == "Prescription")
            {
                if (charge.ExecuteStatus == YesNoEnum.Y) //  已发药
                {
                    // 处方退费 //  已发药 // 去药房退药 
                    await UpdateRefundStatus(input.ApplyId, 3); // 审核完成
                }
                else
                {
                    var refundDto=charge.Adapt<OutpatientChargeRefundDto>();
                    refundDto.Reason = apply.ApplyReason;
                    // 01 修改计费表
                    await chargeService.Refund(refundDto);
                    // 02 修改处方状态
                    await prescriptionService.PrescriptionRefundDrug(new PrescriptionRefundDrugDto()
                    {
                        PrescriptionId = charge.BillingId,
                        Reason = apply.ApplyReason,
                        RefundUserId = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0"),
                    });
                    // 03 完成退费
                    await UpdateRefundStatus(input.ApplyId, 4);
                   
                }

            }
            else
            {
                var refundDto=charge.Adapt<OutpatientChargeRefundDto>();
                refundDto.Reason = apply.ApplyReason;
                // 01 修改计费表
                await chargeService.Refund(refundDto);
                // 02 修改申请单状态
                //
                
                //03 完成退费
                await UpdateRefundStatus(input.ApplyId, 4);
            }
            
            if (apply.BillingType == "Prescription" && charge.ExecuteStatus == YesNoEnum.Y)
            {
                // 处方退费 //  已发药 // 去药房退药
                await UpdateRefundStatus(input.ApplyId, 3); // 审核完成
            }
            else
            {
                var refundDto=charge.Adapt<OutpatientChargeRefundDto>();
                refundDto.Reason = apply.ApplyReason;
                // 修改计费表
                await chargeService.Refund(refundDto);
                // 完成退费
                await UpdateRefundStatus(input.ApplyId, 4);
            }
        }

        var entity = input.Adapt<RefundAudit>();
        await refundAuditRep.UpdateAsync(entity);
        return entity.Id;
    }

    private async Task UpdateRefundStatus(long? applyId, int auditStatus)
    {
        // 0 待审核 1 审核中(审核通过) 2审核驳回  3  审核完成  4  退费完成
        await refundApplyRep.UpdateAsync(
            u => new RefundApply()
            {
                AuditStatus = auditStatus // 完成退费
            }, u => u.Id == applyId);
    }

    /// <summary>
    /// 删除退费审核表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除退费审核表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteRefundAuditInput input)
    {
        var entity = await refundAuditRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await refundAuditRep.FakeDeleteAsync(entity); //假删除
        //await _refundAuditRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除退费审核表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除退费审核表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteRefundAuditInput> input)
    {
        var exp = Expressionable.Create<RefundAudit>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await refundAuditRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await refundAuditRep.FakeDeleteAsync(list); //假删除
        //return await _refundAuditRep.DeleteAsync(list);   //真删除
    }
}