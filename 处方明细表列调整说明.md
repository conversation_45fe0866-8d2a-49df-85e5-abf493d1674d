# 处方明细表列调整说明

## 调整概述

根据后端 `SkinTestResultDetailOutput` 类的属性顺序，调整了皮试管理页面中处方明细表的列显示，确保前端表格列与后端数据模型保持一致。

## 后端数据模型

```csharp
public class SkinTestResultDetailOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 处方主表Id
    /// </summary>
    public long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 药品Id
    /// </summary>
    public long? DrugId { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 单次量
    /// </summary>
    public decimal? SingleDose { get; set; }
    
    /// <summary>
    /// 单次量单位
    /// </summary>
    public string? SingleDoseUnit { get; set; }

    /// <summary>
    /// 给药途径Id
    /// </summary>
    public long? MedicationRoutesId { get; set; }

    /// <summary>
    /// 给药途径名称
    /// </summary>
    public string? MedicationRoutesName { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    public string? FrequencyName { get; set; }

    /// <summary>
    /// 皮试结果
    /// </summary>
    public string? SkinTestResults { get; set; }

    /// <summary>
    /// 皮试时间
    /// </summary>
    public DateTime? SkinTestTime { get; set; }
}
```

## 调整前后对比

### 调整前
```vue
<el-table-column prop="drugName" label="药品名称" width="200" />
<el-table-column prop="drugSpec" label="规格" width="120" />
<el-table-column label="数量" width="100">
    <template #default="{ row }"> {{ row.quantity }}{{ row.unit }} </template>
</el-table-column>
<el-table-column label="是否需要皮试" width="120">...</el-table-column>
<el-table-column label="皮试结果" width="100">...</el-table-column>
<el-table-column prop="skinTestTime" label="皮试时间" width="180" />
<el-table-column prop="skinTestStaffName" label="皮试人员" width="100" />
<el-table-column prop="skinTestMethod" label="皮试方法" width="120" />
<el-table-column prop="reactionDescription" label="反应描述" width="150" />
```

### 调整后
```vue
<el-table-column prop="drugCode" label="药品编码" width="120" />
<el-table-column prop="drugName" label="药品名称" width="200" />
<el-table-column prop="spec" label="规格" width="120" />
<el-table-column prop="unit" label="单位" width="80" />
<el-table-column prop="quantity" label="数量" width="80" />
<el-table-column label="单次量" width="100">
    <template #default="{ row }">
        {{ row.singleDose }}{{ row.singleDoseUnit }}
    </template>
</el-table-column>
<el-table-column prop="medicationRoutesName" label="给药途径" width="100" />
<el-table-column prop="frequencyName" label="频次" width="100" />
<el-table-column label="皮试结果" width="100">
    <template #default="{ row }">
        <el-tag v-if="row.skinTestResults" :type="row.skinTestResults === '1' ? 'success' : row.skinTestResults === '2' ? 'danger' : 'warning'">
            {{ getSkinTestResultText(row.skinTestResults) }}
        </el-tag>
        <span v-else style="color: #999">未录入</span>
    </template>
</el-table-column>
<el-table-column prop="skinTestTime" label="皮试时间" width="180">
    <template #default="scope">
        {{ scope.row.skinTestTime ? formatDate(scope.row.skinTestTime, 'YYYY-MM-DD HH:mm:ss') : '' }}
    </template>
</el-table-column>
```

## 主要调整内容

### 1. 列顺序调整
按照后端 `SkinTestResultDetailOutput` 类的属性顺序重新排列表格列：

1. **药品编码** (DrugCode) - 新增
2. **药品名称** (DrugName)
3. **规格** (Spec)
4. **单位** (Unit) - 独立显示
5. **数量** (Quantity) - 独立显示
6. **单次量** (SingleDose + SingleDoseUnit) - 新增
7. **给药途径** (MedicationRoutesName) - 新增
8. **频次** (FrequencyName) - 新增
9. **皮试结果** (SkinTestResults)
10. **皮试时间** (SkinTestTime)

### 2. 新增列
- **药品编码**：显示药品的唯一编码
- **单次量**：显示单次用药剂量和单位
- **给药途径**：显示给药方式（如口服、静脉注射等）
- **频次**：显示用药频次（如每日三次等）

### 3. 列宽优化
- **药品编码**：120px (新增)
- **药品名称**：200px (保持)
- **规格**：120px (保持)
- **单位**：80px (独立显示，缩小)
- **数量**：80px (独立显示，缩小)
- **单次量**：100px (新增)
- **给药途径**：100px (新增)
- **频次**：100px (新增)
- **皮试结果**：100px (保持)
- **皮试时间**：180px (保持)

### 4. 数据格式化优化
- **单次量**：组合显示 `singleDose + singleDoseUnit`
- **皮试时间**：格式化为 `YYYY-MM-DD HH:mm:ss`，空值显示为空字符串
- **皮试结果**：使用 `skinTestResults` 字段（注意字段名变更）

### 5. 字段名变更
- `drugSpec` → `spec`
- `skinTestResult` → `skinTestResults`

## 列详细说明

| 序号 | 列名 | 字段名 | 宽度 | 说明 | 数据类型 |
|------|------|--------|------|------|----------|
| 1 | 药品编码 | drugCode | 120px | 药品的唯一标识编码 | string |
| 2 | 药品名称 | drugName | 200px | 药品的通用名称 | string |
| 3 | 规格 | spec | 120px | 药品规格（如片剂、胶囊等） | string |
| 4 | 单位 | unit | 80px | 药品的计量单位 | string |
| 5 | 数量 | quantity | 80px | 处方开具的药品数量 | decimal |
| 6 | 单次量 | singleDose + singleDoseUnit | 100px | 单次用药剂量及单位 | decimal + string |
| 7 | 给药途径 | medicationRoutesName | 100px | 给药方式名称 | string |
| 8 | 频次 | frequencyName | 100px | 用药频次名称 | string |
| 9 | 皮试结果 | skinTestResults | 100px | 皮试结果（阴性/阳性/可疑） | string |
| 10 | 皮试时间 | skinTestTime | 180px | 皮试执行时间 | DateTime |

## 业务逻辑保持

### 1. 皮试结果显示
- **阴性** (值为 '1')：绿色标签
- **阳性** (值为 '2')：红色标签  
- **可疑** (值为 '3')：橙色标签
- **未录入**：灰色文字显示

### 2. 操作按钮逻辑
- **录入皮试**：当药品需要皮试且未录入结果或状态为已撤销时显示
- **撤销皮试**：当药品已录入皮试结果且状态为有效时显示
- **无需操作**：当药品无需皮试时显示

### 3. 是否需要皮试标识
- 保留原有的"是否需要皮试"列，移至皮试时间后面
- 用于快速识别哪些药品需要进行皮试

## 数据一致性

调整后的表格列完全对应后端 `SkinTestResultDetailOutput` 类的属性，确保：
- 前后端数据字段一致
- 显示顺序与数据模型保持一致
- 数据类型和格式正确处理
- 字段命名规范统一

## 用户体验优化

1. **信息完整性**：显示更多药品相关信息，便于医护人员全面了解用药情况
2. **数据清晰性**：单位和数量分开显示，信息更清晰
3. **专业性**：增加给药途径和频次信息，符合医疗专业需求
4. **操作便捷性**：保持原有的皮试操作功能不变

## 总结

通过这次调整，处方明细表的显示更加完整和专业，与后端数据模型保持高度一致。新增的药品编码、单次量、给药途径、频次等信息使得医护人员能够更全面地了解药品使用情况，提升了系统的专业性和实用性。
