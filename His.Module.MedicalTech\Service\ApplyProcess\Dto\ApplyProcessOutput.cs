﻿namespace His.Module.MedicalTech.Service;

/// <summary>
/// 申请单输出参数
/// </summary>
public class ApplyProcessOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>
    public string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    public long RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    public string VisitNo { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    public string OutpatientNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string PatientName { get; set; }

    /// <summary>
    /// 费别名称
    /// </summary>
    public string FeeName { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    public DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    public string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    public long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    public string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 状态 字典 ApplyStatus
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 申请单类型
    /// </summary>
    public string? ApplyType { get; set; }
}

/// <summary>
/// 申请单明细输出参数
/// </summary>
public class ApplyProcessDetailOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>

    public string ApplyNo { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>

    public long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>

    public string ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>

    public string ItemName { get; set; }

    /// <summary>
    /// 单价
    /// </summary>

    public decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>

    public decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>

    public decimal? Amount { get; set; }

    /// <summary>
    /// 套餐项目
    /// </summary>
    public List<ApplyProcessPackageItemOutput> ApplyProcessPackageItemOutputs { get; set; }
}

/// <summary>
/// 申请单套餐项目输出参数
/// </summary>
public class ApplyProcessPackageItemOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 检查明细Id
    /// </summary>

    public long DetailsId { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>

    public string ApplyNo { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>

    public long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>

    public string ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>

    public string ItemName { get; set; }

    /// <summary>
    /// 单价
    /// </summary>

    public decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>

    public decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>

    public decimal? Amount { get; set; }
}