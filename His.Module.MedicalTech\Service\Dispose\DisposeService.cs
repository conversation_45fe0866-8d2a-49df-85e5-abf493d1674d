﻿using Furion.DatabaseAccessor;
using His.Module.MedicalTech.Api.Dispose;
using His.Module.MedicalTech.Api.Dispose.Dto;
using His.Module.MedicalTech.Enum;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.Shared.Api.Api.ChargeItem;
using Yitter.IdGenerator;
namespace His.Module.MedicalTech.Service;

/// <summary>
/// 处置服务 🧩
/// </summary>
[ApiDescriptionSettings(MedicalTechConst.GroupName, Order = 100)]
public class DisposeService : IDynamicApiController, ITransient, IDisposeApi
{
    private readonly SqlSugarRepository<Dispose> _disposeRep;
    private readonly SqlSugarRepository<DisposePackageItem> _disposePackageItemRep;
    private readonly IChargeItemApi _chargeItemApi;
    private readonly IChargeApi _chargeApi;

    public DisposeService(SqlSugarRepository<Dispose> disposeRep
        , IChargeItemApi chargeItemApi
        , IChargeApi chargeApi
        , SqlSugarRepository<DisposePackageItem> disposePackageItemRep)
    {
        _disposeRep = disposeRep;
        _chargeItemApi = chargeItemApi;
        _disposePackageItemRep = disposePackageItemRep;
        _chargeApi = chargeApi;
    }

    /// <summary>
    /// 分页查询处置 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询处置")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DisposeOutput>> Page(PageDisposeInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _disposeRep.AsQueryable()
            .Where(u => u.RegisterId == input.RegisterId)
            .Where(u => u.BillingDoctorId == long.Parse(App.User.FindFirst(ClaimConst.UserId).Value))
            .Where(u => u.BillingDeptId == long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value))
            .Where(u => u.BillingTime >= DateTime.Today)
            .Where(u => u.Flag == input.Flag)
            .OrderBy(u => u.ApplyNo)
            .Select<DisposeOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取处置详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取处置详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<Dispose> Detail([FromQuery] QueryByIdDisposeInput input)
    {
        return await _disposeRep.AsQueryable()
            .Includes(u => u.DisposePackageItem)
            .FirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加处置 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加处置")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost, UnitOfWork]
    public async Task Add(List<AddDisposeInput> input)
    {
        // 将输入对象映射为 Dispose 实体列
        var entities = input.Adapt<List<Dispose>>();

        // 批量获取所有关联的收费项目详细信息（优化数据库查询，避免N+1问题）
        var chargeItems = await _chargeItemApi.GetDetails([.. entities.Select(u => (long)u.ItemId)]);

        // 遍历处理每个处置实体
        foreach (var entity in entities)
        {
            // 根据 ItemId 匹配对应的收费项目
            var chargeItem = chargeItems.FirstOrDefault(u => u.Id == entity.ItemId);

            // 生成分布式唯一ID
            entity.Id = YitIdHelper.NextId();

            // 从数据库序列生成申请单号（格式化为8位数字，不足补零）
            entity.ApplyNo = await _disposeRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('apply_no_seq') As varchar), 8, '0')");

            // 填充收费项目基础信息
            entity.ItemCode = chargeItem.Code;
            entity.ItemName = chargeItem.Name;
            entity.Spec = chargeItem.Spec;
            entity.Unit = chargeItem.Unit;
            entity.Manufacturer = chargeItem.Manufacturer;
            entity.Model = chargeItem.Model;
            entity.Price = chargeItem.Price;

            // 计算总金额 = 数量 × 单价
            entity.Amount = entity.Quantity * chargeItem.Price;

            // 转换套餐标识为可空整型
            entity.IsPackage = (int?)chargeItem.Package;

            // 设置业务时间相关字段
            entity.BillingTime = DateTime.Now;

            // 从用户凭证中获取并设置计费部门信息
            entity.BillingDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId)?.Value ?? "0");
            entity.BillingDeptName = App.User.FindFirst(ClaimConst.OrgName)?.Value;

            // 从用户凭证中获取并设置开单医生信息
            entity.BillingDoctorId = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");
            entity.BillingDoctorName = App.User.FindFirst(ClaimConst.RealName)?.Value;

            // 设置收费分类和初始状态
            entity.ChargeCategoryId = chargeItem.ChargeCategoryId;
            entity.Status = 1;

            // 处理套餐项目（当收费项目是套餐时）
            if (chargeItem.Package == YesNoEnum.Y)
            {
                // 转换套餐子项为处置包项实体
                var disposePackageItems = chargeItem.ChargeItemPacks.Select(packageItem => new DisposePackageItem
                {
                    // 关联主处置记录
                    DisposeId = entity.Id,
                    ApplyNo = entity.ApplyNo,

                    // 填充套餐子项信息
                    ItemId = packageItem.Id,
                    ItemCode = packageItem.Code,
                    ItemName = packageItem.Name,
                    Spec = packageItem.Spec,
                    Unit = packageItem.Unit,
                    Manufacturer = packageItem.Manufacturer,
                    Model = packageItem.Model,
                    Price = packageItem.Price,
                    Quantity = entity.Quantity * packageItem.Quantity,

                    // 计算子项金额
                    Amount = entity.Quantity * packageItem.Quantity * packageItem.Price,

                    // 继承收费分类
                    ChargeCategoryId = packageItem.ChargeCategoryId,
                }).ToList();

                // 批量插入套餐子项记录
                await _disposePackageItemRep.InsertRangeAsync(disposePackageItems);
            }
        }

        // 批量插入主处置记录（优化数据库操作）
        await _disposeRep.InsertRangeAsync(entities);
    }

    /// <summary>
    /// 更新处置 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新处置")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost, UnitOfWork]
    public async Task Update(UpdateDisposeInput input)
    {
        var record = await _disposeRep.GetFirstAsync(u => u.Id == input.Id)
            ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (record.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0001);
        }
        var entity = input.Adapt<Dispose>();
        await _disposeRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除处置 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除处置")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost, UnitOfWork]
    public async Task Delete(DeleteDisposeInput input)
    {
        var entity = await _disposeRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0002);
        }
        await _disposeRep.FakeDeleteAsync(entity);   //假删除

        // 获取并假删除套餐项目记录
        var packageItemsList = await _disposePackageItemRep.GetListAsync(u => u.DisposeId == input.Id);
        await _disposePackageItemRep.FakeDeleteAsync(packageItemsList);
        //await _disposeRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除处置 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除处置")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列 不能为空")] List<DeleteDisposeInput> input)
    {
        var exp = Expressionable.Create<Dispose>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _disposeRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _disposeRep.FakeDeleteAsync(list);   //假删除
        //return await _disposeRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置申请单状态 🔄
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置申请单状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetStatus(SetDisposeStatusInput input)
    {
        var entity = await _disposeRep.GetFirstAsync(u => u.Id == input.Id)
                ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (!MedicalTechConst.ApplyStatusValidTransitions.TryGetValue((int)entity.Status, out var allowed)
            || !allowed.Contains((int)input.Status))
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0004);
        }
        await _disposeRep.UpdateAsync(u => new Dispose()
        {
            Status = input.Status,
        }, u => u.Id == entity.Id);
    }

    /// <summary>
    /// 处置收费 💰
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("处置收费")]
    [ApiDescriptionSettings(Name = "Charge"), HttpPost, UnitOfWork]
    public async Task Charge(ChargeDisposeInput input)
    {
        // 查询主记录及其明细
        var main = await _disposeRep.AsQueryable()
            .Includes(u => u.DisposePackageItem)
            .FirstAsync(u => u.Id == input.Id)
            ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (main.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0006);
        }

        // 构建 DTO 对象
        var dto = main.Adapt<OutpatientChargeDto>();
        dto.BillingType = "Dispose";
        dto.BillingId = main.Id;
        dto.BillingNo = main.ApplyNo;
        dto.TotalAmount = main.Amount;
        dto.InvoiceNumber = input.InvoiceNumber;
        // 处理套餐项目
        if (main.IsPackage == 1)
        {
            dto.Details = main.DisposePackageItem.Select(item => new OutpatientChargeDetailDto
            {
                ItemId = item.ItemId,
                ItemCode = item.ItemCode,
                ItemName = item.ItemName,
                Spec = item.Spec,
                Unit = item.Unit,
                Price = item.Price,
                Quantity = item.Quantity,
                Amount = item.Amount,
                BillingId = item.DisposeId,
                BillingNo = item.ApplyNo,
                BillingTime = main.BillingTime,
                BillingDeptId = main.BillingDeptId,
                BillingDoctorId = main.BillingDoctorId,
                ExecuteDeptId = main.ExecuteDeptId,
                BillingDetailId = item.Id,
                BillingDoctorName = main.BillingDoctorName,
                ChargeCategoryId = item.ChargeCategoryId,
                PackId = main.ItemId,
                PackCode = main.ItemCode,
                PackName = main.ItemName,
                PackNumber = (short?)(item.Quantity / main.Quantity),
                PackPrice = main.Price,
            }).ToList();
        }
        // 处理非套餐项目
        else if (main.IsPackage == 2)
        {
            var detail = new OutpatientChargeDetailDto()
            {
                ItemId = main.ItemId,
                ItemCode = main.ItemCode,
                ItemName = main.ItemName,
                Spec = main.Spec,
                Unit = main.Unit,
                Price = main.Price,
                Quantity = main.Quantity,
                Amount = main.Amount,
                BillingId = main.Id,
                BillingNo = main.ApplyNo,
                BillingTime = main.BillingTime,
                BillingDeptId = main.BillingDeptId,
                BillingDoctorId = main.BillingDoctorId,
                ExecuteDeptId = main.ExecuteDeptId,
                BillingDetailId = 0, // 非套餐收费时明细ID为0
                BillingDoctorName = main.BillingDoctorName,
                ChargeCategoryId = main.ChargeCategoryId,
                FrequencyId = main.FrequencyId,
                Manufacturer = main.Manufacturer,
                MedicationDays = (short?)main.Days,
            };
            dto.Details = [detail];
        }

        // 调用收费接口
        var result = await _chargeApi.Add(dto);

        // 更新主记录状态和收费信息
        await _disposeRep.UpdateAsync(u => new Dispose()
        {
            Status = 2,
            ChargeTime = result.ChargeTime,
            ChargeStaffId = result.ChargeStaffId,
            ChargeStaffName = result.ChargeStaffName,
        }, u => u.Id == input.Id);
    }


    /// <summary>
    /// 处置收费 💰
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("处置收费")]
    [ApiDescriptionSettings(IgnoreApi = true)]
    public async Task Charge(ChargeDisposeDto input)
    {
        await Charge(new ChargeDisposeInput
        {
            Id = input.Id, InvoiceNumber = input.InvoiceNumber
        });
    }
}