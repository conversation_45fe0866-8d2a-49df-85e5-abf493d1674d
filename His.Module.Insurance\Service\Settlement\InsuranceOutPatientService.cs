using His.Module.Insurance.Const;
using His.Module.Insurance.Entity;
using His.Module.Insurance.Service.Settlement.Dto.Outpatient;
using His.Module.Insurance.Service.Settlement.Dto.Patient;
using His.Module.Insurance.Service.Settlement.Model.Patient;
using His.Module.OutpatientDoctor.Api.Register;
using His.Module.OutpatientDoctor.Api.Register.Dto;
using Newtonsoft.Json;

namespace His.Module.Insurance.Service.Settlement;
/// <summary>
/// 医保患者
/// </summary>
[ApiDescriptionSettings(InsuranceConst.GroupName, Order = 100)]
public class InsuranceOutPatientService(
    IOutpatientApi outpatientApi,
    IOutpatientRegisterApi outpatientRegisterApi,
    ICommonApi commonApi,
    SqlSugarRepository<PersonalInfo> personalInfoRepository
    ): IDynamicApiController, ITransient
{
 
    /// <summary>
    /// 获取门诊医保对应后的项目
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取门诊医保对应后的项目")]
    [ApiDescriptionSettings(Name = "GetMatchedItems"), HttpPost]
    public async Task<List<SettleMzPreFypd>> GetMatchedItems(OutpatientChargeSettlementInput input)
    {
        // 获取收费项目
      List<ChargeDetailDto> chargeItems =  await outpatientRegisterApi.GetChargeItems(input.RegCategoryId);
        //对照医保目录

        var result = new List<SettleMzPreFypd>();
        foreach (var item in chargeItems)
        {
            var fypd = new SettleMzPreFypd()
            {
                yyxmbm = item.ItemCode,
                yyxmmc = item.ItemName,
                dj = (double)item.Price,
                sl = (double)item.Quantity,
                zje = (double)item.Amount,
                zxksbm = input.ExecuteDeptId.ToString(),
                kdksbm = input.BillingDeptId.ToString(),
                zxysbm = input.ExecuteDoctorId.ToString(),
                kdysbm = input.BillingDoctorId.ToString(),
                

            };
            result.Add(fypd);
        }
      
        return result;
    }

    
}