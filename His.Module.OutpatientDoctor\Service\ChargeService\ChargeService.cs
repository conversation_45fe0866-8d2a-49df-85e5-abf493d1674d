using Furion.DatabaseAccessor;
using His.Module.Financial.Api.MedicalCard;
using His.Module.Financial.Api.MedicalCard.Dto;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.OutpatientDoctor.Service.Dto;
using His.Module.Patient.Api.Api;
namespace His.Module.OutpatientDoctor.Service;

/// <summary>
/// 计费服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class ChargeService(
    SqlSugarRepository<ChargeMain> chargeMainRep,
    SqlSugarRepository<ChargeDetail> chargeDetailRep,
    ICardInfoApi cardInfoApi,
    IMedicalCardPaymentApi medicalCardPaymentApi,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient, IChargeApi
{
    private readonly ISqlSugarClient _sqlSugarClient = sqlSugarClient;


    /// <summary>
    /// 获取门诊收费记录 ℹ️
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取门诊收费记录")]
    [ApiDescriptionSettings(Name = "Get"), HttpGet]
    public async Task<ChargeMain> Get([FromQuery] long id)
    {
        return await chargeMainRep.GetFirstAsync(u => u.Id == id);
    }

    /// <summary>
    /// 获取门诊收费明细 ℹ️
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取门诊收费明细")]
    [ApiDescriptionSettings(Name = "GetDetail"), HttpGet]
    public async Task<List<ChargeDetail>> GetDetail([FromQuery] long id)
    {
        return await chargeDetailRep.AsQueryable().Where(u => u.ChargeId == id)
                .ToListAsync()
            ;
    }

    /// <summary>
    /// 计费 ➕
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [DisplayName("计费")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<OutpatientChargeOutput> Add(OutpatientChargeDto dto)
    {
        //构造收费主表实体
        var main = dto.Adapt<ChargeMain>();

        if (string.IsNullOrWhiteSpace(dto.InvoiceNumber))
            main.InvoiceNumber = await GetInvoiceNumber();
        else
            main.InvoiceNumber = dto.InvoiceNumber;


        main.PayMethod1Id = -1;
        main.PayAmount1 = 0;
        // main.   PayMethod2Id = input.PaymentMethod;
        // main.  PayAmount2 = input.PersonalPayment;
        main.Status = ChargeStatusEnum.Charged;
        main.Type = 1;


        //插入收费主表
        await chargeMainRep.InsertAsync(main);
        var details = dto.Details.Adapt<List<ChargeDetail>>();
        var output = new OutpatientChargeOutput
        {
            ChargeId = main.Id,
            ChargeStaffId = main.CreateUserId,
            ChargeTime = main.CreateTime,
            ChargeStaffName = main.CreateUserName,
            Details = []
        };

        foreach (var item in details)
        {
            item.ChargeId = main.Id;
            item.ExecuteStatus = YesNoEnum.N;
            item.Withdrawal = YesNoEnum.N;
            item.PayMethod1Id = main.PayMethod1Id;
            item.PayAmount1 = main.PayAmount1;
            item.PayMethod2Id = main.PayMethod2Id;
            item.PayAmount2 = main.PayAmount2;
            item.Status = ChargeStatusEnum.Charged;
            await chargeDetailRep.InsertAsync(item);
            output.Details.Add(new OutpatientChargeDetailsOutput()
            {
                ChargeDetailId = item.Id, ChargeId = item.ChargeId, ItemId = item.ItemId
            });
        }

        // var cardInfoEntity = await cardInfoApi.Detail(dto.CardId);
        // //扣除费用
        // //处理卡扣款情况
        // if (dto.PaymentMethod ==658657048305733)// 650961344696389)
        // {
        //     //卡扣款
        //     await cardInfoApi.CardPay(new CardPayInput()
        //     {
        //         Id = cardInfoEntity.Id,
        //         InvoiceNumber = main.InvoiceNumber,
        //         PayAmount = main.PayAmount2,
        //         PayMethodId = dto.PaymentMethod,
        //         // PayMethodId =  main.PayMethod1Id, 
        //     });
        // }


        return output;
    }

    [ApiDescriptionSettings(IgnoreApi = true), UnitOfWork]
    public async Task<bool> Refund(OutpatientChargeRefundDto dto)
    {
        var chargeMain = await chargeMainRep.GetFirstAsync(x => x.Id == dto.ChargeId);
        // 卡交易记录
        await medicalCardPaymentApi.Refund(new MedicalCardPaymentRefundDto()
        {
            PatientId =  chargeMain.PatientId??0,
            CardNo = chargeMain.CardNo, 
            PayAmount = chargeMain.PayAmount2, 
            VisitNo = chargeMain.VisitNo,
        });

        // 插入一条负值 ，

 
        var chargeDetail = await chargeDetailRep.AsQueryable().Where(x => x.ChargeId == dto.ChargeId).ToListAsync();
        var refundMain = chargeMain.Adapt<ChargeMain>();
        refundMain.ResetNull(); 
        refundMain.OriginalRecordId = dto.ChargeId;
        refundMain.PayAmount1 = -chargeMain.PayAmount1;
        refundMain.PayAmount2 = -chargeMain.PayAmount2; // 退款金额
        refundMain.Status = ChargeStatusEnum.Refund;
        refundMain.RefundReason = dto.Reason; // 退款原因
        await chargeMainRep.InsertAsync(refundMain);
        foreach (var detail in chargeDetail)
        {
            var refundDetail = detail.Adapt<ChargeDetail>();
            refundDetail.ResetNull(); 
            refundDetail.OriginalDetailsId = detail.Id;
            refundDetail.PayAmount1 = -detail.PayAmount1;
            refundDetail.PayAmount2 = -detail.PayAmount2; // 退款金额
            refundDetail.ChargeId = refundMain.Id;
            refundDetail.Status = ChargeStatusEnum.Refund;
            refundDetail.RefundReason = dto.Reason; // 退款原因
            await chargeDetailRep.InsertAsync(refundDetail);
        }
        // 修改原始记录

        await chargeDetailRep.UpdateAsync(
            u => new ChargeDetail() { Status = ChargeStatusEnum.Refund, }
            , u => u.ChargeId == dto.ChargeId
        );
        return await chargeMainRep.UpdateAsync(
            u => new ChargeMain() { Status = ChargeStatusEnum.Refund, }
            , u => u.Id == dto.ChargeId
        );
    }
 

    public async Task<bool> ExecuteStatus(OutpatientChargeExecuteDto dto)
    {
        var main = await chargeMainRep.GetFirstAsync(x => x.Id == dto.ChargeId);

        if (main == null)
            throw new Exception("未找到该收费单");

        await chargeDetailRep.UpdateAsync(
            u => new ChargeDetail()
            {
                ExecuteTime = dto.ExecuteTime,
                ExecuteStatus = YesNoEnum.Y,
                ExecuteDeptId = dto.ExecuteDeptId,
                ExecuteDoctorId = dto.ExecuteDoctorId,
                // ExecuteTime = dto.ExecuteTime
            },
            u => u.ChargeId == dto.ChargeId
        );
        return await chargeMainRep.UpdateAsync(
            u => new ChargeMain()
            {
                ExecuteDeptId = dto.ExecuteDeptId,
                ExecuteDoctorId = dto.ExecuteDoctorId,
                ExecuteTime = dto.ExecuteTime,
                ExecuteStatus = YesNoEnum.Y,
            },
            u => u.Id == dto.ChargeId
        );
    }

    private async Task<string> GetInvoiceNumber()
    {
        return await chargeMainRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('charge_main_in_num_seq')As varchar),8,'0')");
    }

    //  /// <summary>
    // ///  查询已计费数据
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("查询已计费数据")]
    // [ApiDescriptionSettings(Name = "List"), HttpPost]
    // public async Task<List<ChargeOutput>> List(QueryChargeInput input)
    // {
    //     
    //     var query = chargeMainRep.AsQueryable()
    //         .WhereIF(input.Status is { Count: > 0 }, u => input.Status.Contains((int)u.Status))
    //         .InnerJoin<Register>((c,r)=> c.RegisterId == r.Id)
    //         .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), (c,r) => r.VisitNo==input.VisitNo.Trim())
    //         .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo),  (c,r) => r.OutpatientNo==input.OutpatientNo.Trim())
    //    
    //       
    //         .Select<ChargeOutput>();
    //     
    //     // 获取主表数据
    //     var list = await query.ToListAsync();
    //     if (list.IsNullOrEmpty()) return list;
    //     var ids = list.Select(p => p.Id).ToList();
    //     var details = await chargeDetailRep.AsQueryable()
    //         .InnerJoin<ChargeMain>((d, m) => d.ChargeMainId == m.Id)
    //         .WhereIF(ids.Count > 0, (d, m) => ids.Contains(m.Id)).ToListAsync();
    //     // 遍历list 将details 赋值给list
    //     list.ForEach(item=>
    //             item.Details = details.
    //                 Where(u => u.ChargeMainId == item.Id).ToList().Adapt<List<ChargeDetailOutput>>()
    //         );
    //  
    //      
    //   
    //     return list;
    // }
    /// <summary>
    ///  
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询已计费数据用于退费申请")]
    [ApiDescriptionSettings(Name = "ListOfRefund"), HttpPost]
    public async Task<List<ChargeOfRefundOutput>> ListOfRefund(QueryChargeInput input)
    {
        var query = chargeMainRep.AsQueryable()
            .WhereIF(input.Status is { Count: > 0 }, u => input.Status.Contains((int)u.Status))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), (c) => c.VisitNo == input.VisitNo.Trim())
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo),
                (c) => c.OutpatientNo == input.OutpatientNo.Trim())
            .LeftJoin<RefundApply>((c, r) => c.Id == r.ChargeId)
            .Select((c, r) => new ChargeOfRefundOutput()
            {
                ChargeId = c.Id,
                RefundApplyId = r.Id,
                ApplyReason = r.ApplyReason,
                CreateTime = c.CreateTime,
                Id = c.Id,
                ApplyTime = r.ApplyTime,
                ApplyUserName = r.CreateUserName,
                ApplyStatus = r.Status,
                AuditStatus = r.AuditStatus,
            }, true);
        var list = await query.ToListAsync();
        if (list.IsNullOrEmpty()) return list;
        var ids = list.Select(p => p.Id).ToList();
        var details = await chargeDetailRep.AsQueryable()
            .InnerJoin<ChargeMain>((d, m) => d.ChargeId == m.Id)
            .WhereIF(ids.Count > 0, (d, m) => ids.Contains(m.Id)).ToListAsync();
        // 遍历list 将details 赋值给list
        list.ForEach(item =>
        {
            item.Details = details.Where(u => u.ChargeId == item.Id).ToList().Adapt<List<ChargeDetailOutput>>();
        });


        return list;
    }
}